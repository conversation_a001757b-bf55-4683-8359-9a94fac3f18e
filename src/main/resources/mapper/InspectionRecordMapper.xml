<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.InspectionRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.InspectionRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="asset_id" property="assetId" jdbcType="VARCHAR"/>
        <result column="inspection_type" property="inspectionType" jdbcType="VARCHAR"/>
        <result column="inspection_number" property="inspectionNumber" jdbcType="INTEGER"/>
        <result column="inspection_time" property="inspectionTime" jdbcType="TIMESTAMP"/>
        <result column="detection_result" property="detectionResult" jdbcType="INTEGER"/>
        <result column="found_markings_json" property="foundMarkingsJson" jdbcType="LONGVARCHAR"/>
        <result column="found_markings_count" property="foundMarkingsCount" jdbcType="INTEGER"/>
        <result column="expected_markings_count" property="expectedMarkingsCount" jdbcType="INTEGER"/>
        <result column="completion_rate" property="completionRate" jdbcType="DECIMAL"/>
        <result column="end_marking_name" property="endMarkingName" jdbcType="VARCHAR"/>
        <result column="detection_record_id" property="detectionRecordId" jdbcType="BIGINT"/>
        <result column="gps_latitude" property="gpsLatitude" jdbcType="DECIMAL"/>
        <result column="gps_longitude" property="gpsLongitude" jdbcType="DECIMAL"/>
        <result column="is_completed" property="isCompleted" jdbcType="INTEGER"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
        <result column="expected_lanes_json" property="expectedLanesJson" jdbcType="LONGVARCHAR"/>
        <result column="first_detection_time" property="firstDetectionTime" jdbcType="TIMESTAMP"/>
        <result column="verification_timeout" property="verificationTimeout" jdbcType="TIMESTAMP"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, asset_id, inspection_type, inspection_number, inspection_time, detection_result,
        found_markings_json, found_markings_count, expected_markings_count, completion_rate,
        end_marking_name, detection_record_id, gps_latitude, gps_longitude, is_completed,
        sign_name, expected_lanes_json, first_detection_time, verification_timeout, created_time
    </sql>

    <!-- 查询资产的最新N次巡查记录 -->
    <select id="selectLatestByAssetId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inspection_records
        WHERE asset_id = #{assetId}
        ORDER BY inspection_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取资产的下一个巡查次数 -->
    <select id="getNextInspectionNumberByAsset" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(inspection_number), 0) + 1
        FROM inspection_records
        WHERE asset_id = #{assetId}
    </select>

    <!-- 删除资产的旧巡查记录（保留最新N条） -->
    <delete id="deleteOldAssetRecords">
        DELETE FROM inspection_records
        WHERE asset_id = #{assetId}
          AND id NOT IN (
              SELECT id FROM (
                  SELECT id
                  FROM inspection_records
                  WHERE asset_id = #{assetId}
                  ORDER BY inspection_time DESC
                  LIMIT #{keepCount}
              ) AS latest_records
          )
    </delete>

    <!-- 根据巡检类型查询记录 -->
    <select id="selectByInspectionType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inspection_records
        WHERE inspection_type = #{inspectionType}
        ORDER BY inspection_time DESC
    </select>

    <!-- 查找指定位置附近的进行中标牌巡检 -->
    <select id="findNearbyActiveSignInspections" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inspection_records
        WHERE is_completed = 0
          AND inspection_type = '对应性'
          AND gps_latitude IS NOT NULL
          AND gps_longitude IS NOT NULL
          <if test="signName != null and signName != ''">
          AND sign_name = #{signName}
          </if>
          AND (
            6371000 * acos(
              cos(radians(#{latitude})) * cos(radians(gps_latitude)) *
              cos(radians(gps_longitude) - radians(#{longitude})) +
              sin(radians(#{latitude})) * sin(radians(gps_latitude))
            )
          ) &lt;= #{distance}
        ORDER BY (
          6371000 * acos(
            cos(radians(#{latitude})) * cos(radians(gps_latitude)) *
            cos(radians(gps_longitude) - radians(#{longitude})) +
            sin(radians(#{latitude})) * sin(radians(gps_latitude))
          )
        )
    </select>

    <!-- 查询超时的巡检记录 -->
    <select id="selectTimeoutInspections" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM inspection_records
        WHERE is_completed = 0
          AND verification_timeout IS NOT NULL
          AND verification_timeout &lt; NOW()
        ORDER BY verification_timeout
    </select>

    <!-- 根据标牌名称和位置查询最新的巡检次数 -->
    <select id="getNextInspectionNumberBySignAndLocation" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(inspection_number), 0) + 1
        FROM inspection_records
        WHERE sign_name = #{signName}
          AND gps_latitude IS NOT NULL
          AND gps_longitude IS NOT NULL
          AND (
            6371000 * acos(
              cos(radians(#{latitude})) * cos(radians(gps_latitude)) *
              cos(radians(gps_longitude) - radians(#{longitude})) +
              sin(radians(#{latitude})) * sin(radians(gps_latitude))
            )
          ) &lt;= #{distance}
    </select>

</mapper>
