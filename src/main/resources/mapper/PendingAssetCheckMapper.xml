<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.PendingAssetCheckMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.PendingAssetCheck">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="gps_track_id" property="gpsTrackId" jdbcType="VARCHAR"/>
        <result column="expected_asset_id" property="expectedAssetId" jdbcType="VARCHAR"/>
        <result column="asset_type" property="assetType" jdbcType="VARCHAR"/>
        <result column="asset_name" property="assetName" jdbcType="VARCHAR"/>
        <result column="expected_latitude" property="expectedLatitude" jdbcType="DECIMAL"/>
        <result column="expected_longitude" property="expectedLongitude" jdbcType="DECIMAL"/>
        <result column="detected_latitude" property="detectedLatitude" jdbcType="DECIMAL"/>
        <result column="detected_longitude" property="detectedLongitude" jdbcType="DECIMAL"/>
        <result column="passed_time" property="passedTime" jdbcType="TIMESTAMP"/>
        <result column="check_type" property="checkType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="confirmed_detection_id" property="confirmedDetectionId" jdbcType="BIGINT"/>
        <result column="confirmed_time" property="confirmedTime" jdbcType="TIMESTAMP"/>
        <result column="missing_reason" property="missingReason" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, device_id, gps_track_id, expected_asset_id, asset_type, asset_name,
        expected_latitude, expected_longitude, detected_latitude, detected_longitude, passed_time, check_type, status,
        confirmed_detection_id, confirmed_time, missing_reason, created_time
    </sql>

    <!-- 批量插入待确认资产检查 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO pending_asset_checks (
            device_id,gps_track_id, expected_asset_id, asset_type, asset_name,
            gps_latitude, gps_longitude, passed_time, status, created_time
        ) VALUES
        <foreach collection="checks" item="check" separator=",">
            (
                #{check.deviceId},#{check.gpsTrackId}, #{check.expectedAssetId}, #{check.assetType},
                #{check.assetName}, #{check.gpsLatitude}, #{check.gpsLongitude},
                #{check.passedTime}, #{check.status}, #{check.createdTime}
            )
        </foreach>
    </insert>

    <!-- 根据状态查询待确认检查 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE status = #{status}
        ORDER BY passed_time DESC
    </select>

    <!-- 查询超时的待确认检查 -->
    <select id="findTimeoutPendingChecks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE status = '待确认'
        AND passed_time &lt; #{timeoutBefore}
        ORDER BY passed_time ASC
    </select>

    <!-- 查找指定位置和时间范围内的待确认检查 -->
    <select id="searchForNearbyRecordsToBeConfirmedByTime" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE status = '待确认'
        AND (
            6371000 * ACOS(
                COS(RADIANS(#{latitude})) * COS(RADIANS(gps_latitude)) * 
                COS(RADIANS(gps_longitude) - RADIANS(#{longitude})) + 
                SIN(RADIANS(#{latitude})) * SIN(RADIANS(gps_latitude))
            )
        ) &lt;= #{distance}
        AND passed_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY passed_time ASC
    </select>





    <!-- 批量确认检查记录 -->
    <update id="batchConfirmChecks">
        UPDATE pending_asset_checks
        SET status = '已确认',
            confirmed_detection_id = #{detectionRecordId},
            confirmed_time = #{confirmedTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>



    <!-- 批量标记为缺失 -->
    <update id="batchMarkAsMissing">
        UPDATE pending_asset_checks
        SET status = '缺失',
            confirmed_time = #{confirmedTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计各状态的检查数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM pending_asset_checks
        GROUP BY status
    </select>







    <!-- 根据检查类型和状态查询超时任务 -->
    <select id="findTimeoutChecks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE check_type = #{checkType}
          AND status = #{status}
          AND passed_time &lt; #{timeoutTime}
        ORDER BY passed_time ASC
    </select>

    <!-- 根据设备ID和状态查询 -->
    <select id="selectByDeviceIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE device_id = #{deviceId}
          AND status = #{status}
        ORDER BY created_time DESC
    </select>

    <!-- 根据状态查询 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE status = #{status}
        ORDER BY created_time DESC
    </select>

    <!-- 统计各种状态的检查任务数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count
        FROM pending_asset_checks
        GROUP BY status
    </select>

    <!-- 查找附近的待检查任务 -->
    <select id="findNearbyPendingChecks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM pending_asset_checks
        WHERE check_type = #{checkType}
          AND status = #{status}
          AND gps_latitude IS NOT NULL
          AND gps_longitude IS NOT NULL
          AND (
            6371000 * acos(
              cos(radians(#{latitude})) * cos(radians(gps_latitude)) *
              cos(radians(gps_longitude) - radians(#{longitude})) +
              sin(radians(#{latitude})) * sin(radians(gps_latitude))
            )
          ) &lt;= #{distance}
        ORDER BY (
          6371000 * acos(
            cos(radians(#{latitude})) * cos(radians(gps_latitude)) *
            cos(radians(gps_longitude) - radians(#{longitude})) +
            sin(radians(#{latitude})) * sin(radians(gps_latitude))
          )
        )
    </select>

</mapper>
