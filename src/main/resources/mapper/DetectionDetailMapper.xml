<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.DetectionDetailMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.DetectionDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="detection_record_id" property="detectionRecordId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="model_source" property="modelSource" jdbcType="VARCHAR"/>
        <result column="class_id" property="classId" jdbcType="INTEGER"/>
        <result column="bbox_x1" property="bboxX1" jdbcType="DOUBLE"/>
        <result column="bbox_y1" property="bboxY1" jdbcType="DOUBLE"/>
        <result column="bbox_x2" property="bboxX2" jdbcType="DOUBLE"/>
        <result column="bbox_y2" property="bboxY2" jdbcType="DOUBLE"/>
        <result column="track_id" property="trackId" jdbcType="INTEGER"/>
        <result column="traffic_asset_id" property="trafficAssetId" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, detection_record_id, type, name, model_source, class_id,
        bbox_x1, bbox_y1, bbox_x2, bbox_y2, track_id, traffic_asset_id, created_time
    </sql>

    <!-- 批量插入检测详情 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO detection_details (
            detection_record_id, type, name, model_source, class_id,
            bbox_x1, bbox_y1, bbox_x2, bbox_y2, track_id, traffic_asset_id, created_time
        ) VALUES
        <foreach collection="details" item="detail" separator=",">
            (
                #{detail.detectionRecordId}, #{detail.type}, #{detail.name},
                #{detail.modelSource}, #{detail.classId}, #{detail.bboxX1},
                #{detail.bboxY1}, #{detail.bboxX2}, #{detail.bboxY2},
                #{detail.trackId}, #{detail.trafficAssetId}, #{detail.createdTime}
            )
        </foreach>
    </insert>

    <!-- 根据检测记录ID查询详情列表 -->
    <select id="selectByDetectionRecordId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM detection_details
        WHERE detection_record_id = #{detectionRecordId}
        ORDER BY created_time ASC
    </select>

    <!-- 更新关联的资产ID -->
    <update id="updateTrafficAssetId">
        UPDATE detection_details
        SET traffic_asset_id = #{trafficAssetId}
        WHERE id = #{id}
    </update>

</mapper>
