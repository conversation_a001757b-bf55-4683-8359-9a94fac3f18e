<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.DetectionRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.DetectionRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="frame_id" property="frameId" jdbcType="VARCHAR"/>
        <result column="timestamp" property="timestamp" jdbcType="BIGINT"/>
        <result column="gps_latitude" property="gpsLatitude" jdbcType="DECIMAL"/>
        <result column="gps_longitude" property="gpsLongitude" jdbcType="DECIMAL"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="process_status" property="processStatus" jdbcType="VARCHAR"/>
        <result column="raw_data" property="rawData" jdbcType="LONGVARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, frame_id, timestamp, gps_latitude, gps_longitude, device_id,
        image_url, process_status, raw_data, created_time, updated_time
    </sql>

    <!-- 更新处理状态 -->
    <update id="updateProcessStatus">
        UPDATE detection_records
        SET process_status = #{processStatus}
        WHERE id = #{id}
    </update>

</mapper>
