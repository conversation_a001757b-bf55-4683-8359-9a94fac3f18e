<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.AlertRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.AlertRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="alert_type" property="alertType" jdbcType="VARCHAR"/>
        <result column="asset_id" property="assetId" jdbcType="VARCHAR"/>
        <result column="alert_level" property="alertLevel" jdbcType="VARCHAR"/>
        <result column="alert_message" property="alertMessage" jdbcType="LONGVARCHAR"/>
        <result column="alert_data" property="alertData" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="resolved_time" property="resolvedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, alert_type, asset_id, alert_level, alert_message, alert_data,
        status, created_time, resolved_time
    </sql>


</mapper>
