<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ybda.mapper.ImageReviewTaskMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ybda.model.entity.ImageReviewTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="asset_id" property="assetId" jdbcType="VARCHAR"/>
        <result column="current_images_json" property="currentImagesJson" jdbcType="VARCHAR"/>
        <result column="new_image_url" property="newImageUrl" jdbcType="VARCHAR"/>
        <result column="detection_record_id" property="detectionRecordId" jdbcType="BIGINT"/>
        <result column="review_status" property="reviewStatus" jdbcType="VARCHAR"/>
        <result column="reviewer_id" property="reviewerId" jdbcType="VARCHAR"/>
        <result column="review_time" property="reviewTime" jdbcType="TIMESTAMP"/>
        <result column="review_reason" property="reviewReason" jdbcType="VARCHAR"/>
        <result column="selected_images_json" property="selectedImagesJson" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, asset_id, current_images_json, new_image_url, detection_record_id,
        review_status, reviewer_id, review_time, review_reason, selected_images_json, created_time
    </sql>

    <!-- 查询待审核的任务列表 -->
    <select id="selectPendingTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_review_tasks
        WHERE review_status = '待审核'
        ORDER BY created_time DESC
    </select>

    <!-- 根据资产ID查询审核任务 -->
    <select id="selectByAssetId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM image_review_tasks
        WHERE asset_id = #{assetId}
        ORDER BY created_time DESC
    </select>

    <!-- 更新审核状态 -->
    <update id="updateReviewStatus">
        UPDATE image_review_tasks
        SET review_status = #{status},
            reviewer_id = #{reviewerId},
            review_time = NOW(),
            review_reason = #{reason}
        WHERE id = #{id}
    </update>

</mapper>
