package com.ybda.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具类
 * 统一处理时间格式和时区
 */
public class TimeUtils {
    
    /** 中国时区 */
    public static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai");
    
    /** 标准时间格式 */
    public static final String STANDARD_PATTERN = "yyyy-MM-dd HH:mm:ss";
    
    /** 文件名时间格式 */
    public static final String FILENAME_PATTERN = "yyyyMMdd_HHmmss";
    
    /** 标准时间格式化器 */
    public static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern(STANDARD_PATTERN);
    
    /** 文件名时间格式化器 */
    public static final DateTimeFormatter FILENAME_FORMATTER = DateTimeFormatter.ofPattern(FILENAME_PATTERN);
    
    /**
     * 获取当前中国时间
     * 
     * @return 当前LocalDateTime
     */
    public static LocalDateTime now() {
        return LocalDateTime.now(CHINA_ZONE);
    }
    
    /**
     * 格式化时间为标准字符串
     * 
     * @param dateTime 时间
     * @return 格式化后的字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public static String formatStandard(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(STANDARD_FORMATTER);
    }
    
    /**
     * 格式化时间为文件名字符串
     * 
     * @param dateTime 时间
     * @return 格式化后的字符串 (yyyyMMdd_HHmmss)
     */
    public static String formatFilename(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(FILENAME_FORMATTER);
    }
    
    /**
     * 解析标准格式的时间字符串
     * 
     * @param timeString 时间字符串 (yyyy-MM-dd HH:mm:ss)
     * @return LocalDateTime
     */
    public static LocalDateTime parseStandard(String timeString) {
        if (timeString == null || timeString.trim().isEmpty()) {
            return null;
        }
        return LocalDateTime.parse(timeString, STANDARD_FORMATTER);
    }
    
    /**
     * 获取今天开始时间 (00:00:00)
     * 
     * @return 今天开始时间
     */
    public static LocalDateTime todayStart() {
        return now().toLocalDate().atStartOfDay();
    }
    
    /**
     * 获取今天结束时间 (23:59:59)
     * 
     * @return 今天结束时间
     */
    public static LocalDateTime todayEnd() {
        return now().toLocalDate().atTime(23, 59, 59);
    }
    
    /**
     * 获取昨天开始时间
     * 
     * @return 昨天开始时间
     */
    public static LocalDateTime yesterdayStart() {
        return now().minusDays(1).toLocalDate().atStartOfDay();
    }
    
    /**
     * 获取昨天结束时间
     * 
     * @return 昨天结束时间
     */
    public static LocalDateTime yesterdayEnd() {
        return now().minusDays(1).toLocalDate().atTime(23, 59, 59);
    }
    
    /**
     * 获取指定天数前的开始时间
     * 
     * @param days 天数
     * @return 指定天数前的开始时间
     */
    public static LocalDateTime daysAgoStart(int days) {
        return now().minusDays(days).toLocalDate().atStartOfDay();
    }
    
    /**
     * 获取当前时间的文件名格式字符串
     * 
     * @return 当前时间的文件名格式 (yyyyMMdd_HHmmss)
     */
    public static String nowFilename() {
        return formatFilename(now());
    }
    
    /**
     * 获取当前时间的标准格式字符串
     * 
     * @return 当前时间的标准格式 (yyyy-MM-dd HH:mm:ss)
     */
    public static String nowStandard() {
        return formatStandard(now());
    }
}
