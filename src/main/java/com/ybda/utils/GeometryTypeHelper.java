package com.ybda.utils;

/**
 * 几何类型辅助工具类
 * 替代原来的GeometryType枚举，提供几何类型相关的工具方法
 */
public class GeometryTypeHelper {
    
    // 几何类型常量
    public static final String POINT_ASSET = "点状资产";
    public static final String LINE_ASSET = "线状资产";
    public static final String POLYGON_ASSET = "面状资产";
    
    /**
     * 根据资产类型推断几何类型
     */
    public static String inferFromAssetType(String assetType) {
        return switch (assetType) {
            case "barrier" -> LINE_ASSET;           // 护栏通常是线状
            case "ground_marking" -> // 地面标线可能是点状（停车线）或线状（车道线）
                // 默认点状，具体可根据名称判断
                    POINT_ASSET;
            case "overhead_sign" -> POINT_ASSET;    // 标志牌通常是点状
            case "traffic_light" -> POINT_ASSET;    // 信号灯通常是点状
            default -> POINT_ASSET;                 // 默认点状
        };
    }
    
    /**
     * 根据名称进一步判断几何类型
     */
    public static String inferFromName(String assetType, String name) {
        if ("ground_marking".equals(assetType) && name != null) {
            if (name.contains("车道线") || name.contains("实线") || name.contains("虚线")) {
                return LINE_ASSET;
            }
        }
        if ("barrier".equals(assetType)) {
            return LINE_ASSET; // 护栏都是线状
        }
        return inferFromAssetType(assetType);
    }
    
    /**
     * 判断是否为点状资产
     */
    public static boolean isPointAsset(String geometryType) {
        return POINT_ASSET.equals(geometryType);
    }
    
    /**
     * 判断是否为线状资产
     */
    public static boolean isLineAsset(String geometryType) {
        return LINE_ASSET.equals(geometryType);
    }
    
    /**
     * 判断是否为面状资产
     */
    public static boolean isPolygonAsset(String geometryType) {
        return POLYGON_ASSET.equals(geometryType);
    }
}
