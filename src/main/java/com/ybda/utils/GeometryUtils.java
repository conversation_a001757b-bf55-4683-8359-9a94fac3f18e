package com.ybda.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.model.entity.TrafficAsset;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 几何计算工具类
 */
@Slf4j
public class GeometryUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 判断GPS点是否在资产范围内
     * @param gpsPoint GPS坐标点
     * @param asset 资产
     * @param maxDistance 最大距离（米）
     * @return 是否在范围内
     */
    public static boolean isPointNearAsset(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        if (gpsPoint == null || asset == null) {
            return false;
        }
        
        try {
            String geometryType = asset.getGeometryType();

            if (GeometryTypeHelper.isPointAsset(geometryType)) {
                return isPointNearPoint(gpsPoint, asset, maxDistance);
            } else if (GeometryTypeHelper.isLineAsset(geometryType)) {
                return isPointNearLine(gpsPoint, asset, maxDistance);
            } else if (GeometryTypeHelper.isPolygonAsset(geometryType)) {
                return isPointInPolygon(gpsPoint, asset, maxDistance);
            } else {
                log.warn("未知的几何类型: {}", asset.getGeometryType());
                return false;
            }
        } catch (Exception e) {
            log.error("判断GPS点是否在资产范围内失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }

    /**
     * 获取线状资产的代表坐标点（用于存储到PendingAssetCheck）
     * 根据检测位置选择最近的线段中点
     *
     * @param asset 线状资产
     * @param detectionPoint 检测位置（可选，如果为null则返回线状资产的中心点）
     * @return 代表坐标点
     */
    public static CoordinatePoint getLineAssetRepresentativePoint(TrafficAsset asset, CoordinatePoint detectionPoint) {
        try {
            if (asset.getGeometryCoordinates() == null) {
                log.warn("线状资产缺少坐标数据: assetId={}", asset.getAssetId());
                return null;
            }

            // 解析线状坐标
            List<CoordinatePoint> linePoints = objectMapper.readValue(
                asset.getGeometryCoordinates(),
                new TypeReference<List<CoordinatePoint>>() {}
            );

            if (linePoints.isEmpty()) {
                return null;
            }

            // 如果只有一个点，直接返回
            if (linePoints.size() == 1) {
                return linePoints.get(0);
            }

            // 如果没有检测点，返回线状资产的中心点
            if (detectionPoint == null) {
                return calculateCenterPoint(linePoints);
            }

            // 找到距离检测点最近的线段，返回该线段的中点
            double minDistance = Double.MAX_VALUE;
            CoordinatePoint closestSegmentMidpoint = null;

            for (int i = 0; i < linePoints.size() - 1; i++) {
                CoordinatePoint p1 = linePoints.get(i);
                CoordinatePoint p2 = linePoints.get(i + 1);

                // 计算线段中点
                CoordinatePoint midpoint = new CoordinatePoint(
                    (p1.getLat() + p2.getLat()) / 2.0,
                    (p1.getLng() + p2.getLng()) / 2.0
                );

                // 计算检测点到线段的距离
                double distance = distanceFromPointToLineSegment(detectionPoint, p1, p2);

                if (distance < minDistance) {
                    minDistance = distance;
                    closestSegmentMidpoint = midpoint;
                }
            }

            log.debug("线状资产代表点: assetId={}, 最近线段中点=({}, {}), 距离={}米",
                asset.getAssetId(),
                closestSegmentMidpoint != null ? closestSegmentMidpoint.getLat() : "null",
                closestSegmentMidpoint != null ? closestSegmentMidpoint.getLng() : "null",
                minDistance);

            return closestSegmentMidpoint != null ? closestSegmentMidpoint : calculateCenterPoint(linePoints);

        } catch (Exception e) {
            log.error("计算线状资产代表点失败: assetId={}", asset.getAssetId(), e);
            return null;
        }
    }

    /**
     * 计算坐标点列表的中心点
     */
    private static CoordinatePoint calculateCenterPoint(List<CoordinatePoint> points) {
        if (points.isEmpty()) {
            return null;
        }

        double totalLat = 0.0;
        double totalLng = 0.0;

        for (CoordinatePoint point : points) {
            totalLat += point.getLat();
            totalLng += point.getLng();
        }

        return new CoordinatePoint(
            totalLat / points.size(),
            totalLng / points.size()
        );
    }

    /**
     * 判断GPS点是否接近点状资产
     */
    private static boolean isPointNearPoint(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        if (asset.getLatitude() == null || asset.getLongitude() == null) {
            return false;
        }
        
        CoordinatePoint assetPoint = new CoordinatePoint(asset.getLatitude(), asset.getLongitude());
        double distance = gpsPoint.distanceTo(assetPoint);
        
        log.debug("点状资产距离计算: GPS({}, {}) -> 资产({}, {}) = {}米", 
            gpsPoint.getLat(), gpsPoint.getLng(),
            asset.getLatitude(), asset.getLongitude(), distance);
        
        return distance <= maxDistance;
    }
    
    /**
     * 判断GPS点是否接近线状资产
     */
    private static boolean isPointNearLine(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        try {
            if (asset.getGeometryCoordinates() == null) {
                log.warn("线状资产缺少坐标数据: assetId={}", asset.getAssetId());
                return false;
            }
            
            // 解析线状坐标
            List<CoordinatePoint> linePoints = objectMapper.readValue(
                asset.getGeometryCoordinates(),
                    new TypeReference<>() {
                    }
            );
            
            if (linePoints.size() < 2) {
                log.warn("线状资产坐标点不足: assetId={}, pointCount={}, 按点状资产处理",
                    asset.getAssetId(), linePoints.size());
                // 如果只有1个坐标点，按点状资产处理
                if (linePoints.size() == 1) {
                    CoordinatePoint singlePoint = linePoints.get(0);
                    double distance = gpsPoint.distanceTo(singlePoint);
                    return distance <= maxDistance;
                }
                return false;
            }
            
            // 计算GPS点到线段的最短距离
            double minDistance = Double.MAX_VALUE;
            
            for (int i = 0; i < linePoints.size() - 1; i++) {
                CoordinatePoint p1 = linePoints.get(i);
                CoordinatePoint p2 = linePoints.get(i + 1);
                
                double distance = distanceFromPointToLineSegment(gpsPoint, p1, p2);
                minDistance = Math.min(minDistance, distance);
                
                // 如果已经在范围内，直接返回
                if (distance <= maxDistance) {
                    log.debug("GPS点在线状资产范围内: 距离={}米, 线段=({},{}) -> ({},{})", 
                        distance, p1.getLat(), p1.getLng(), p2.getLat(), p2.getLng());
                    return true;
                }
            }
            
            log.debug("GPS点不在线状资产范围内: 最短距离={}米, 要求距离<={}米", minDistance, maxDistance);
            return false;
            
        } catch (Exception e) {
            log.error("解析线状资产坐标失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }
    
    /**
     * 判断GPS点是否在面状资产内
     */
    private static boolean isPointInPolygon(CoordinatePoint gpsPoint, TrafficAsset asset, double maxDistance) {
        try {
            if (asset.getGeometryCoordinates() == null) {
                log.warn("面状资产缺少坐标数据: assetId={}", asset.getAssetId());
                return false;
            }
            
            // 解析多边形坐标
            List<CoordinatePoint> polygonPoints = objectMapper.readValue(
                asset.getGeometryCoordinates(),
                    new TypeReference<>() {
                    }
            );
            
            if (polygonPoints.size() < 3) {
                log.warn("面状资产坐标点不足: assetId={}, pointCount={}", 
                    asset.getAssetId(), polygonPoints.size());
                return false;
            }
            
            // 使用射线法判断点是否在多边形内
            boolean inside = isPointInPolygonRayCasting(gpsPoint, polygonPoints);
            
            if (inside) {
                log.debug("GPS点在面状资产内部");
                return true;
            }
            
            // 如果不在内部，检查是否在边界附近
            double minDistance = Double.MAX_VALUE;
            for (int i = 0; i < polygonPoints.size(); i++) {
                CoordinatePoint p1 = polygonPoints.get(i);
                CoordinatePoint p2 = polygonPoints.get((i + 1) % polygonPoints.size());
                
                double distance = distanceFromPointToLineSegment(gpsPoint, p1, p2);
                minDistance = Math.min(minDistance, distance);
            }
            
            boolean nearBoundary = minDistance <= maxDistance;
            log.debug("GPS点{}面状资产边界: 最短距离={}米", nearBoundary ? "接近" : "远离", minDistance);
            
            return nearBoundary;
            
        } catch (Exception e) {
            log.error("解析面状资产坐标失败: assetId={}", asset.getAssetId(), e);
            return false;
        }
    }
    
    /**
     * 计算点到线段的最短距离
     */
    private static double distanceFromPointToLineSegment(CoordinatePoint point, CoordinatePoint lineStart, CoordinatePoint lineEnd) {
        // 将地理坐标转换为平面坐标进行计算（简化处理）
        double x = point.getLng();
        double y = point.getLat();
        double x1 = lineStart.getLng();
        double y1 = lineStart.getLat();
        double x2 = lineEnd.getLng();
        double y2 = lineEnd.getLat();
        
        double A = x - x1;
        double B = y - y1;
        double C = x2 - x1;
        double D = y2 - y1;
        
        double dot = A * C + B * D;
        double lenSq = C * C + D * D;
        
        if (lenSq == 0) {
            // 线段退化为点
            return point.distanceTo(lineStart);
        }
        
        double param = dot / lenSq;
        
        CoordinatePoint closestPoint;
        if (param < 0) {
            closestPoint = lineStart;
        } else if (param > 1) {
            closestPoint = lineEnd;
        } else {
            closestPoint = new CoordinatePoint(
                y1 + param * D,
                x1 + param * C
            );
        }
        
        return point.distanceTo(closestPoint);
    }
    
    /**
     * 使用射线法判断点是否在多边形内
     */
    private static boolean isPointInPolygonRayCasting(CoordinatePoint point, List<CoordinatePoint> polygon) {
        double x = point.getLng();
        double y = point.getLat();
        
        boolean inside = false;
        int j = polygon.size() - 1;
        
        for (int i = 0; i < polygon.size(); i++) {
            double xi = polygon.get(i).getLng();
            double yi = polygon.get(i).getLat();
            double xj = polygon.get(j).getLng();
            double yj = polygon.get(j).getLat();
            
            if (((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
            j = i;
        }
        
        return inside;
    }
}
