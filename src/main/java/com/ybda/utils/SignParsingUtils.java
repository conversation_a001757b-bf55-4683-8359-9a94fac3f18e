package com.ybda.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标牌解析工具类
 * 用于解析空中标牌的车道指示信息
 */
@Slf4j
public class SignParsingUtils {

    /**
     * 车道方向枚举
     */
    public enum LaneDirection {
        // 基本方向
        LEFT("左转", "左", "L"),
        STRAIGHT("直行", "直", "S"),
        RIGHT("右转", "右", "R"),
        U_TURN("掉头", "U"),

        // 常用组合
        LEFT_STRAIGHT("左转直行", "左直", "LS"),
        STRAIGHT_RIGHT("直行右转", "直右", "SR"),
        LEFT_U_TURN("左转掉头", "左掉", "LU"),

        // 其他
        UNKNOWN("未知", "未知", "UNKNOWN");

        private final String[] keywords;

        LaneDirection(String... keywords) {
            this.keywords = keywords;
        }

        public String[] getKeywords() {
            return keywords;
        }
    }

    /**
     * 车道信息
     */
    public static class LaneInfo {
        private int laneNumber;
        private LaneDirection direction;
        private String originalText;

        // 无参构造函数（JSON反序列化需要）
        public LaneInfo() {}

        public LaneInfo(int laneNumber, LaneDirection direction, String originalText) {
            this.laneNumber = laneNumber;
            this.direction = direction;
            this.originalText = originalText;
        }

        // Getters and Setters
        public int getLaneNumber() { return laneNumber; }
        public void setLaneNumber(int laneNumber) { this.laneNumber = laneNumber; }

        public LaneDirection getDirection() { return direction; }
        public void setDirection(LaneDirection direction) { this.direction = direction; }

        public String getOriginalText() { return originalText; }
        public void setOriginalText(String originalText) { this.originalText = originalText; }

        @Override
        public String toString() {
            return String.format("车道%d:%s", laneNumber, direction != null ? direction.keywords[0] : "未知");
        }
    }

    /**
     * 解析空中标牌的车道指示信息
     * 支持格式: "第1车道掉头掉头, 第2车道左转+掉头, 第3车道左转+掉头, 第4车道直行, 第5车道直行, 第6车道右转"
     */
    public static List<LaneInfo> parseOverheadSign(String signName) {
        List<LaneInfo> laneInfos = new ArrayList<>();

        if (signName == null || signName.trim().isEmpty()) {
            return laneInfos;
        }

        try {
            log.debug("🔍 开始解析标牌: '{}'", signName);

            // 使用正则表达式匹配: 第X车道[方向信息]
            Pattern pattern = Pattern.compile("第(\\d+)车道([^,，]+)");
            Matcher matcher = pattern.matcher(signName);

            while (matcher.find()) {
                int laneNumber = Integer.parseInt(matcher.group(1));
                String directionText = matcher.group(2).trim();

                log.debug("🔍 提取车道: 车道{} → 方向='{}'", laneNumber, directionText);

                // 直接映射到主要方向
                LaneDirection direction = mapToMainDirection(directionText);
                laneInfos.add(new LaneInfo(laneNumber, direction, directionText));

                log.debug("  ✅ 车道{}: '{}' → {}", laneNumber, directionText, direction.keywords[0]);
            }

            log.info("🏷️ 标牌解析完成: '{}' → {} 个车道信息", signName, laneInfos.size());

        } catch (Exception e) {
            log.error("❌ 标牌解析失败: signName={}", signName, e);
        }

        return laneInfos;
    }



    /**
     * 检查地面标线是否与空中标牌匹配
     */
    public static boolean isGroundMarkingMatchingSign(List<LaneInfo> signLanes, String groundMarkingName) {
        if (signLanes.isEmpty() || groundMarkingName == null) {
            return false;
        }

        // 解析地面标线的方向
        LaneDirection groundDirection = parseGroundMarkingDirection(groundMarkingName);
        if (groundDirection == null) {
            return false;
        }

        // 检查是否有匹配的车道方向
        boolean matches = signLanes.stream()
            .anyMatch(lane -> lane.getDirection() == groundDirection);

        log.debug("🔍 地面标线匹配检查: '{}' ({}) vs 标牌车道 → {}",
            groundMarkingName, groundDirection.keywords[0], matches ? "匹配" : "不匹配");

        return matches;
    }

    /**
     * 解析地面标线的方向
     */
    private static LaneDirection parseGroundMarkingDirection(String markingName) {
        String normalizedName = markingName.toLowerCase();

        if (normalizedName.contains("左转") || normalizedName.contains("左") || normalizedName.contains("left")) {
            return LaneDirection.LEFT;
        }
        if (normalizedName.contains("直行") || normalizedName.contains("直") || normalizedName.contains("straight")) {
            return LaneDirection.STRAIGHT;
        }
        if (normalizedName.contains("右转") || normalizedName.contains("右") || normalizedName.contains("right")) {
            return LaneDirection.RIGHT;
        }
        if (normalizedName.contains("掉头") || normalizedName.contains("u")) {
            return LaneDirection.U_TURN;
        }

        return null;
    }

    /**
     * 生成期望的地面标线列表
     */
    public static List<String> generateExpectedGroundMarkings(List<LaneInfo> signLanes) {
        List<String> expectedMarkings = new ArrayList<>();

        for (LaneInfo lane : signLanes) {
            String expectedMarking = String.format("车道%d%s箭头", lane.getLaneNumber(), lane.getDirection().keywords[0]);
            expectedMarkings.add(expectedMarking);
        }

        return expectedMarkings;
    }

    /**
     * 简单映射方向文本到枚举
     * 只识别常用的基本方向和复合方向
     */
    private static LaneDirection mapToMainDirection(String directionText) {
        String text = directionText.toLowerCase().trim();

        log.debug("🔍 映射方向文本: '{}' → '{}'", directionText, text);

        // 复合方向（有+号的）
        if (text.contains("左") && text.contains("直")) {
            log.debug("  → 匹配到: LEFT_STRAIGHT");
            return LaneDirection.LEFT_STRAIGHT;
        }
        if (text.contains("直") && text.contains("右")) {
            log.debug("  → 匹配到: STRAIGHT_RIGHT");
            return LaneDirection.STRAIGHT_RIGHT;
        }
        if (text.contains("左") && text.contains("掉")) {
            log.debug("  → 匹配到: LEFT_U_TURN");
            return LaneDirection.LEFT_U_TURN;
        }

        // 基本方向
        if (text.contains("掉头") || text.contains("掉")) {
            log.debug("  → 匹配到: U_TURN");
            return LaneDirection.U_TURN;
        }
        if (text.contains("左转") || text.contains("左")) {
            log.debug("  → 匹配到: LEFT");
            return LaneDirection.LEFT;
        }
        if (text.contains("直行") || text.contains("直")) {
            log.debug("  → 匹配到: STRAIGHT");
            return LaneDirection.STRAIGHT;
        }
        if (text.contains("右转") || text.contains("右")) {
            log.debug("  → 匹配到: RIGHT");
            return LaneDirection.RIGHT;
        }

        log.debug("  → 匹配到: UNKNOWN");
        return LaneDirection.UNKNOWN;
    }



}
