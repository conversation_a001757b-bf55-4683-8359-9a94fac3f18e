package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 交通资产实体类
 * 对应数据库表：traffic_assets
 */
@Data
@TableName("traffic_assets")
public class TrafficAsset {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 资产唯一标识 */
    private String assetId;
    
    /** 资产类型：ground_marking/overhead_sign/traffic_light/barrier */
    private String type;
    
    /** 资产名称 */
    private String name;
    
    /** 资产几何类型：点状资产/线状资产/面状资产 */
    private String geometryType;

    /** 资产纬度（点状资产使用） */
    private Double latitude;

    /** 资产经度（点状资产使用） */
    private Double longitude;

    /** 几何坐标数据（JSON格式存储坐标数组） */
    private String geometryCoordinates;
    
    /** 首次检测时间 */
    private LocalDateTime firstDetectedTime;
    
    /** 最后检测时间 */
    private LocalDateTime lastDetectedTime;
    
    /** 检测次数 */
    private Integer detectionCount;
    
    /** 资产状态：ACTIVE/MISSING/INACTIVE */
    private String status;

    /** 资产是否存在 */
    private Integer available;

    /** 检测图片URL */
    private String detectionImageUrl;

    /** 图片质量审核状态：待审核/审核通过/审核拒绝 */
    private String imageQualityStatus;

    /** 图片审核人ID */
    private String imageReviewerId;

    /** 图片审核时间 */
    private LocalDateTime imageReviewTime;

    /** 图片审核原因 */
    private String imageReviewReason;

    /** 创建时间 */
    private LocalDateTime createdTime;

    /** 更新时间 */
    private LocalDateTime updatedTime;

}
