package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 巡查记录实体类 - 通用资产巡检记录
 */
@Data
@TableName("inspection_records")
public class InspectionRecord {

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 关联的资产ID（通用巡检使用） */
    private String assetId;

    /** 巡检类型：存在性/对应性 */
    private String inspectionType;

    /** 巡查次数（第几次巡查） */
    private Integer inspectionNumber;

    /** 巡查时间 */
    private LocalDateTime inspectionTime;

    /** 检测结果：1=检测到，0=未检测到 */
    private Integer detectionResult;

    /** 发现的地面标线(JSON数组) */
    private String foundMarkingsJson;

    /** 发现的标线数量 */
    private Integer foundMarkingsCount;

    /** 期望的标线数量 */
    private Integer expectedMarkingsCount;

    /** 完成率（0.0-1.0） */
    private BigDecimal completionRate;

    /** 结束标识名称 */
    private String endMarkingName;

    /** 关联的检测记录ID（如果检测到） */
    private Long detectionRecordId;

    /** 巡检时GPS纬度 */
    private Double gpsLatitude;

    /** 巡检时GPS经度 */
    private Double gpsLongitude;

    /** 是否完成：1=完成，0=进行中 */
    private Integer isCompleted;

    /** 标牌名称 */
    private String signName;

    /** 期望的车道信息(JSON) */
    private String expectedLanesJson;

    /** 首次检测时间 */
    private LocalDateTime firstDetectionTime;

    /** 验证超时时间 */
    private LocalDateTime verificationTimeout;

    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /**
     * 检查是否完成
     */
    public boolean isFinished() {
        return isCompleted != null && isCompleted == 1;
    }

    /**
     * 设置完成状态
     */
    public void setFinished(boolean finished) {
        this.isCompleted = finished ? 1 : 0;
    }

    /**
     * 检查是否检测到资产
     */
    public boolean isDetected() {
        return detectionResult != null && detectionResult == 1;
    }

    /**
     * 设置检测结果
     */
    public void setDetected(boolean detected) {
        this.detectionResult = detected ? 1 : 0;
    }

    /**
     * 计算并设置完成率
     */
    public void calculateCompletionRate() {
        if ("存在性".equals(inspectionType)) {
            // 存在性巡检：检测到=100%，未检测到=0%
            this.completionRate = isDetected() ? BigDecimal.ONE : BigDecimal.ZERO;
        } else if ("对应性".equals(inspectionType)) {
            // 对应性巡检：按标线匹配数量计算
            if (expectedMarkingsCount != null && expectedMarkingsCount > 0) {
                double rate = (double) (foundMarkingsCount != null ? foundMarkingsCount : 0) / expectedMarkingsCount;
                this.completionRate = BigDecimal.valueOf(rate).setScale(4, BigDecimal.ROUND_HALF_UP);
            } else {
                this.completionRate = BigDecimal.ZERO;
            }
        } else {
            this.completionRate = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否为存在性巡检
     */
    public boolean isExistenceInspection() {
        return "存在性".equals(inspectionType);
    }

    /**
     * 检查是否为对应性巡检
     */
    public boolean isCorrespondenceInspection() {
        return "对应性".equals(inspectionType);
    }

    /**
     * 检查是否超时
     */
    public boolean isTimeout() {
        return verificationTimeout != null && LocalDateTime.now().isAfter(verificationTimeout);
    }
}
