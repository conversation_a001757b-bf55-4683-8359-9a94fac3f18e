package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 同步记录实体类
 * 记录数据同步的历史信息
 */
@Data
@TableName("sync_records")
public class SyncRecord {
    
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /** 同步类型：EXPORT/IMPORT */
    private String syncType;
    
    /** 同步方向：TO_SERVER/FROM_SERVER */
    private String syncDirection;
    
    /** 同步状态：SUCCESS/FAILED/IN_PROGRESS */
    private String syncStatus;
    
    /** 数据类型：DETECTION_RECORDS/INSPECTION_RECORDS/ASSETS/IMAGES/CONFIG */
    private String dataType;
    
    /** 开始时间 */
    private LocalDateTime startTime;
    
    /** 结束时间 */
    private LocalDateTime endTime;
    
    /** 数据时间范围开始 */
    private LocalDateTime dataStartTime;
    
    /** 数据时间范围结束 */
    private LocalDateTime dataEndTime;
    
    /** 文件路径 */
    private String filePath;
    
    /** 文件大小（字节） */
    private Long fileSize;
    
    /** 处理的记录数量 */
    private Integer recordCount;
    
    /** 成功处理的记录数量 */
    private Integer successCount;
    
    /** 失败的记录数量 */
    private Integer failedCount;
    
    /** 错误信息 */
    private String errorMessage;
    
    /** 同步详情（JSON格式） */
    private String syncDetails;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
    
    /** 更新时间 */
    private LocalDateTime updatedTime;
}
