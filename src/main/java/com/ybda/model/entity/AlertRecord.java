package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.dto.AlertDetailData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;

/**
 * 报警记录实体类
 * 对应数据库表：alert_records
 */
@Slf4j
@Data
@TableName("alert_records")
public class AlertRecord {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 报警类型：ASSET_MISSING/GPS_ABNORMAL/SYSTEM_ERROR */
    private String alertType;

    /** 相关资产ID */
    private String assetId;

    /** 报警级别：LOW/MEDIUM/HIGH/CRITICAL */
    private String alertLevel;

    /** 报警消息 */
    private String alertMessage;

    /** 报警详细信息(JSON格式) */
    private String alertData;

    /** 空中标牌图片URL */
    @com.baomidou.mybatisplus.annotation.TableField("sign_image_url")
    private String signImageUrl;

    /** 地面标线图片URL */
    @com.baomidou.mybatisplus.annotation.TableField("ground_image_url")
    private String groundImageUrl;

    /** 报警状态：ACTIVE/ACKNOWLEDGED/RESOLVED */
    private String status;

    /** 创建时间 */
    private LocalDateTime createdTime;

    /** 解决时间 */
    private LocalDateTime resolvedTime;

    // ========== 便捷方法 ==========

    /**
     * 获取类型化的报警详细数据
     * 将JSON字符串转换为AlertDetailData对象
     */
    public AlertDetailData getAlertDetailData() {
        if (alertData == null || alertData.trim().isEmpty()) {
            return new AlertDetailData();
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(alertData, AlertDetailData.class);
        } catch (JsonProcessingException e) {
            log.warn("解析报警详细数据失败: alertId={}, error={}", id, e.getMessage());
            return new AlertDetailData();
        }
    }

    /**
     * 设置类型化的报警详细数据
     * 将AlertDetailData对象转换为JSON字符串
     */
    public void setAlertDetailData(AlertDetailData detailData) {
        if (detailData == null) {
            this.alertData = null;
            return;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            this.alertData = objectMapper.writeValueAsString(detailData);
        } catch (JsonProcessingException e) {
            log.error("序列化报警详细数据失败: alertId={}, error={}", id, e.getMessage());
            this.alertData = "{}";
        }
    }

    /**
     * 判断是否为对应性检测报警
     */
    public boolean isCorrespondenceAlert() {
        return "CORRESPONDENCE_MISMATCH".equals(alertType);
    }

    /**
     * 判断是否为资产缺失报警
     */
    public boolean isAssetMissingAlert() {
        return "ASSET_MISSING".equals(alertType);
    }

    /**
     * 判断是否为GPS异常报警
     */
    public boolean isGpsAbnormalAlert() {
        return "GPS_ABNORMAL".equals(alertType);
    }

    /**
     * 获取报警级别的中文描述
     */
    public String getAlertLevelText() {
        if (alertLevel == null) {
            return "未知";
        }
        switch (alertLevel) {
            case "LOW": return "低";
            case "MEDIUM": return "中";
            case "HIGH": return "高";
            case "CRITICAL": return "严重";
            default: return alertLevel;
        }
    }

    /**
     * 获取报警状态的中文描述
     */
    public String getStatusText() {
        if (status == null) return "未知";
        switch (status) {
            case "ACTIVE": return "活跃";
            case "ACKNOWLEDGED": return "已确认";
            case "RESOLVED": return "已解决";
            default: return status;
        }
    }

}
