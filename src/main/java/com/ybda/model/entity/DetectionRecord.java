package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 检测记录实体类
 * 对应数据库表：detection_records
 */
@Data
@TableName("detection_records")
public class DetectionRecord {
    
    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 帧ID(每次检测都不同) */
    private String frameId;
    
    /** 检测时间戳 */
    private Long timestamp;
    
    /** 检测时GPS纬度 */
    private Double gpsLatitude;
    
    /** 检测时GPS经度 */
    private Double gpsLongitude;
    
    /** 设备标识 */
    private String deviceId;
    
    /** 检测图片URL */
    private String imageUrl;
    
    /** 处理状态：PENDING/PROCESSED/FAILED */
    private String processStatus;
    
    /** 原始JSON数据 */
    private String rawData;

    /** 创建时间 */
    private LocalDateTime createdTime;

    /** 更新时间 */
    private LocalDateTime updatedTime;
    
    /**
     * 检查GPS位置是否有效
     */
    public boolean hasValidGpsLocation() {
        return gpsLatitude != null && gpsLongitude != null
            && gpsLatitude >= -90 && gpsLatitude <= 90
            && gpsLongitude >= -180 && gpsLongitude <= 180;
    }
}
