package com.ybda.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 图片审核任务实体类
 */
@Data
@TableName("image_review_tasks")
public class ImageReviewTask {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /** 资产ID */
    private String assetId;
    
    /** 当前图片URL */
    private String currentImageUrl;
    
    /** 新检测到的图片URL */
    private String newImageUrl;
    
    /** 检测记录ID */
    private Long detectionRecordId;
    
    /** 审核状态：PENDING/APPROVED/REJECTED */
    private String reviewStatus;
    
    /** 审核人ID */
    private String reviewerId;
    
    /** 审核时间 */
    private LocalDateTime reviewTime;
    
    /** 审核原因 */
    private String reviewReason;
    
    /** 创建时间 */
    private LocalDateTime createdTime;
}
