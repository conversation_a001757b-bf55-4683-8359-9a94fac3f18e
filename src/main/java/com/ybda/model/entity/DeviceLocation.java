package com.ybda.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备位置信息实体类
 * 对应JT808协议T0200位置信息汇报
 */
@Data
@Accessors(chain = true)
public class DeviceLocation {

    private String id;

    /** 设备ID */
    private String deviceId;

    /** 设备手机号 */
    private String mobileNo;

    /** 车牌号 */
    private String plateNo;

    /** 报警标志位 */
    private Integer warnBit;

    /** 状态标志位 */
    private Integer statusBit;

    /** 实际纬度（度） */
    private Double lat;

    /** 实际经度（度） */
    private Double lng;

    /** 高程（米） */
    private Integer altitude;

//    /** 速度（1/10公里每小时） */
//    @Field("speed")
//    private Integer speed;

    /** 实际速度（公里每小时） */
    private Double speedKph;

    /** 方向（0-359度，正北为0，顺时针） */
    private Integer direction;

    /** 设备时间（GPS时间，协议已处理为正确时区） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deviceTime;

    /** 位置附加信息（处理后的可读格式） */
    private Map<String, Object> attributes;

    /** 地址信息（逆地理编码结果） */
    private String address;

    /** 是否有效位置（GPS定位状态） */
    private Boolean isValid;

    /** 卫星数量 */
    private Integer satelliteCount;

    /** 数据接收时间（服务器时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
