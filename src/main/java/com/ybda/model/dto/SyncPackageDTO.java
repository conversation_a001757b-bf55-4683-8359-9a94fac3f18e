package com.ybda.model.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 同步数据包DTO
 * 用于数据导出/导入的数据传输
 */
@Data
public class SyncPackageDTO {
    
    /** 包版本 */
    private String packageVersion;
    
    /** 生成时间 */
    private LocalDateTime generatedTime;
    
    /** 数据时间范围 */
    private LocalDateTime dataStartTime;
    private LocalDateTime dataEndTime;
    
    /** 设备信息 */
    private String deviceId;
    private String deviceName;
    
    /** 检测记录 */
    private List<Map<String, Object>> detectionRecords;
    
    /** 检测详情 */
    private List<Map<String, Object>> detectionDetails;
    
    /** 巡检记录 */
    private List<Map<String, Object>> inspectionRecords;
    
    /** 新增资产 */
    private List<Map<String, Object>> newAssets;
    
    /** 报警记录 */
    private List<Map<String, Object>> alertRecords;
    
    /** 待确认检查记录 */
    private List<Map<String, Object>> pendingAssetChecks;
    
    /** 图片文件信息 */
    private List<ImageFileInfo> imageFiles;
    
    /** 统计信息 */
    private SyncStatistics statistics;
    
    @Data
    public static class ImageFileInfo {
        /** 原始路径 */
        private String originalPath;
        /** 相对路径 */
        private String relativePath;
        /** 文件大小 */
        private Long fileSize;
        /** 文件MD5 */
        private String md5Hash;
        /** 关联的记录ID */
        private Long relatedRecordId;
        /** 关联的记录类型 */
        private String relatedRecordType;
    }
    
    @Data
    public static class SyncStatistics {
        /** 检测记录数量 */
        private Integer detectionRecordCount;
        /** 巡检记录数量 */
        private Integer inspectionRecordCount;
        /** 新增资产数量 */
        private Integer newAssetCount;
        /** 报警记录数量 */
        private Integer alertRecordCount;
        /** 图片文件数量 */
        private Integer imageFileCount;
        /** 总文件大小 */
        private Long totalFileSize;
    }
}
