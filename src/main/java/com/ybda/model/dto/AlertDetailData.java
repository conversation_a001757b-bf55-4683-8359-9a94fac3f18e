package com.ybda.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 报警详细信息数据传输对象
 * 用于替代 AlertRecord 中的 alertData JSON 字符串
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AlertDetailData {

    // ========== 通用报警信息 ==========
    
    /** 报警原因代码 */
    private String alertReason;
    
    /** 报警原因文本描述 */
    private String alertReasonText;
    
    /** GPS纬度 */
    private Double gpsLatitude;
    
    /** GPS经度 */
    private Double gpsLongitude;
    
    /** 报警创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime alertTime;

    // ========== 资产缺失报警专用字段 ==========
    
    /** 检查任务ID */
    private Long checkId;
    
    /** 设备ID */
    private String deviceId;
    
    /** 资产类型 */
    private String assetType;
    
    /** 资产名称 */
    private String assetName;
    
    /** 期望位置信息 */
    private Map<String, Double> expectedLocation;
    
    /** 经过时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime passedTime;
    
    /** 缺失原因 */
    private String missingReason;
    
    /** 超时分钟数 */
    private Integer timeoutMinutes;

    // ========== 对应性检测报警专用字段 ==========
    
    /** 巡检记录ID */
    private Long inspectionId;
    
    /** 标牌名称 */
    private String signName;
    
    /** 巡检次数 */
    private Integer inspectionNumber;
    
    /** 找到的标线数量 */
    private Integer foundCount;
    
    /** 期望的标线数量 */
    private Integer expectedCount;
    
    /** 完成率 */
    private BigDecimal completionRate;
    
    /** 期望的车道信息(JSON字符串) */
    private String expectedLanes;
    
    /** 找到的标线信息(JSON字符串) */
    private String foundMarkings;
    
    /** 空中标牌图片URL */
    private String signImageUrl;
    
    /** 地面标线图片URL */
    private String groundImageUrl;
    
    /** 首次检测时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstDetectionTime;
    
    /** 验证超时时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime verificationTimeout;

    // ========== GPS异常报警专用字段 ==========
    
    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;
    
    /** 异常原因 */
    private String reason;
    
    /** 阈值分钟数 */
    private Integer thresholdMinutes;

    // ========== 报警处理相关字段 ==========
    
    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acknowledgeTime;
    
    /** 确认原因 */
    private String acknowledgeReason;
    
    /** 计划采取的行动 */
    private String plannedAction;
    
    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handlingTime;
    
    /** 处理类型 */
    private String handlingType;
    
    /** 处理备注 */
    private String handlingComment;
    
    /** 调整后的阈值 */
    private Double adjustedThreshold;
    
    /** 解决方案描述 */
    private String resolution;
    
    /** 批量处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime batchProcessTime;
    
    /** 批量处理原因 */
    private String batchProcessReason;

    // ========== 扩展字段 ==========
    
    /** 其他扩展信息 */
    private Map<String, Object> extraData;

    // ========== 便捷方法 ==========
    
    /**
     * 获取匹配度百分比
     */
    public Double getCompletionRatePercent() {
        if (completionRate == null) {
            return null;
        }
        return completionRate.multiply(BigDecimal.valueOf(100)).doubleValue();
    }
    
    /**
     * 判断是否为对应性检测报警
     */
    public boolean isCorrespondenceAlert() {
        return inspectionId != null && signName != null;
    }
    
    /**
     * 判断是否为资产缺失报警
     */
    public boolean isAssetMissingAlert() {
        return checkId != null && assetName != null;
    }
    
    /**
     * 判断是否为GPS异常报警
     */
    public boolean isGpsAbnormalAlert() {
        return "GPS数据异常".equals(reason) || "GPS数据超时或无效".equals(reason);
    }
    
    /**
     * 获取位置描述
     */
    public String getLocationDescription() {
        if (gpsLatitude != null && gpsLongitude != null) {
            return String.format("(%.6f, %.6f)", gpsLatitude, gpsLongitude);
        }
        return null;
    }
    
    /**
     * 获取匹配度描述
     */
    public String getMatchingDescription() {
        if (foundCount != null && expectedCount != null) {
            double rate = expectedCount > 0 ? (foundCount * 100.0 / expectedCount) : 0.0;
            return String.format("%d/%d (%.1f%%)", foundCount, expectedCount, rate);
        }
        return null;
    }
}
