package com.ybda.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 坐标点数据类
 */
@Data
@NoArgsConstructor
public class CoordinatePoint {
    
    /** 纬度 */
    private Double lat;
    
    /** 经度 */
    private Double lng;
    
    /**
     * 简单构造函数（自动规范化精度到6位小数）
     */
    public CoordinatePoint(Double lat, Double lng) {
        this.lat = normalizeCoordinate(lat);
        this.lng = normalizeCoordinate(lng);
    }

    /**
     * 规范化坐标精度到6位小数
     */
    private static Double normalizeCoordinate(Double coordinate) {
        if (coordinate == null) {
            return null;
        }
        return Math.round(coordinate * 1000000.0) / 1000000.0;
    }
    
    /**
     * 计算与另一个点的距离（米）
     */
    public double distanceTo(CoordinatePoint other) {
        if (other == null || other.lat == null || other.lng == null 
            || this.lat == null || this.lng == null) {
            return Double.MAX_VALUE;
        }
        
        double earthRadius = 6371000; // 地球半径（米）
        double lat1Rad = Math.toRadians(this.lat);
        double lat2Rad = Math.toRadians(other.lat);
        double deltaLatRad = Math.toRadians(other.lat - this.lat);
        double deltaLngRad = Math.toRadians(other.lng - this.lng);
        
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return earthRadius * c;
    }
}
