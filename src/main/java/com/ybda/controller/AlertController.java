package com.ybda.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.model.dto.AlertDetailData;
import com.ybda.model.entity.AlertRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报警管理控制器
 * 提供报警记录的查询、确认、解决等功能
 */
@Slf4j
@RestController
@RequestMapping("/api/alerts")
@RequiredArgsConstructor
public class AlertController {

    private final AlertRecordMapper alertRecordMapper;

    /**
     * 获取报警记录列表（分页）
     */
    @GetMapping("/list")
    public Map<String, Object> getAlertList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String alertType,
            @RequestParam(required = false) String alertLevel,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("📋 查询报警记录: page={}, size={}, type={}, level={}, status={}", 
                page, size, alertType, alertLevel, status);
            
            QueryWrapper<AlertRecord> wrapper = new QueryWrapper<>();
            
            // 添加查询条件
            if (alertType != null && !alertType.trim().isEmpty()) {
                wrapper.eq("alert_type", alertType);
            }
            if (alertLevel != null && !alertLevel.trim().isEmpty()) {
                wrapper.eq("alert_level", alertLevel);
            }
            if (status != null && !status.trim().isEmpty()) {
                wrapper.eq("status", status);
            }
            if (startTime != null) {
                wrapper.ge("created_time", startTime);
            }
            if (endTime != null) {
                wrapper.le("created_time", endTime);
            }
            
            // 按创建时间倒序
            wrapper.orderByDesc("created_time");
            
            // 分页查询
            Page<AlertRecord> pageResult = alertRecordMapper.selectPage(
                new Page<>(page, size), wrapper);
            
            response.put("success", true);
            response.put("data", pageResult.getRecords());
            response.put("total", pageResult.getTotal());
            response.put("pages", pageResult.getPages());
            response.put("current", pageResult.getCurrent());
            response.put("size", pageResult.getSize());
            
            log.info("✅ 查询报警记录成功: 总数={}, 当前页={}", pageResult.getTotal(), page);
            
        } catch (Exception e) {
            log.error("❌ 查询报警记录失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取报警统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getAlertStats() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("📊 获取报警统计信息");
            
            // 统计各种状态的报警数量
            Map<String, Object> stats = new HashMap<>();
            
            // 活跃报警
            long activeCount = alertRecordMapper.selectCount(
                new QueryWrapper<AlertRecord>().eq("status", "ACTIVE"));
            stats.put("active", activeCount);
            
            // 已确认报警
            long acknowledgedCount = alertRecordMapper.selectCount(
                new QueryWrapper<AlertRecord>().eq("status", "ACKNOWLEDGED"));
            stats.put("acknowledged", acknowledgedCount);
            
            // 已解决报警
            long resolvedCount = alertRecordMapper.selectCount(
                new QueryWrapper<AlertRecord>().eq("status", "RESOLVED"));
            stats.put("resolved", resolvedCount);
            
            // 按类型统计
            Map<String, Object> typeStats = new HashMap<>();
            List<Map<String, Object>> typeCountList = alertRecordMapper.selectMaps(
                new QueryWrapper<AlertRecord>()
                    .select("alert_type, COUNT(*) as count")
                    .groupBy("alert_type"));
            
            for (Map<String, Object> item : typeCountList) {
                typeStats.put((String) item.get("alert_type"), item.get("count"));
            }
            
            // 按级别统计
            Map<String, Object> levelStats = new HashMap<>();
            List<Map<String, Object>> levelCountList = alertRecordMapper.selectMaps(
                new QueryWrapper<AlertRecord>()
                    .select("alert_level, COUNT(*) as count")
                    .groupBy("alert_level"));
            
            for (Map<String, Object> item : levelCountList) {
                levelStats.put((String) item.get("alert_level"), item.get("count"));
            }
            
            stats.put("byType", typeStats);
            stats.put("byLevel", levelStats);
            
            response.put("success", true);
            response.put("data", stats);
            
            log.info("✅ 获取报警统计成功: 活跃={}, 已确认={}, 已解决={}", 
                activeCount, acknowledgedCount, resolvedCount);
            
        } catch (Exception e) {
            log.error("❌ 获取报警统计失败", e);
            response.put("success", false);
            response.put("message", "获取统计失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 确认报警（带确认原因）
     */
    @PostMapping("/{alertId}/acknowledge")
    public Map<String, Object> acknowledgeAlert(
            @PathVariable Long alertId,
            @RequestParam(required = false) String reason,
            @RequestParam(required = false) String action) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("✅ 确认报警: alertId={}, reason={}, action={}", alertId, reason, action);

            AlertRecord alert = alertRecordMapper.selectById(alertId);
            if (alert == null) {
                response.put("success", false);
                response.put("message", "报警记录不存在");
                return response;
            }

            // 更新报警状态和确认信息
            alert.setStatus("ACKNOWLEDGED");

            // 更新报警详细信息，添加确认原因
            try {
                AlertDetailData alertDetailData = alert.getAlertDetailData();

                alertDetailData.setAcknowledgeTime(LocalDateTime.now());
                alertDetailData.setAcknowledgeReason(reason != null ? reason : "用户确认");
                alertDetailData.setPlannedAction(action != null ? action : "待处理");

                alert.setAlertDetailData(alertDetailData);
            } catch (Exception e) {
                log.warn("更新报警详情失败: {}", e.getMessage());
            }

            alertRecordMapper.updateById(alert);

            response.put("success", true);
            response.put("message", "报警已确认");
            response.put("data", alert);

            log.info("✅ 报警确认成功: alertId={}, type={}, reason={}", alertId, alert.getAlertType(), reason);

        } catch (Exception e) {
            log.error("❌ 确认报警失败: alertId={}", alertId, e);
            response.put("success", false);
            response.put("message", "确认失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 解决报警
     */
    @PostMapping("/{alertId}/resolve")
    public Map<String, Object> resolveAlert(@PathVariable Long alertId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("🔧 解决报警: alertId={}", alertId);
            
            AlertRecord alert = alertRecordMapper.selectById(alertId);
            if (alert == null) {
                response.put("success", false);
                response.put("message", "报警记录不存在");
                return response;
            }
            
            alert.setStatus("RESOLVED");
            alert.setResolvedTime(LocalDateTime.now());
            alertRecordMapper.updateById(alert);
            
            response.put("success", true);
            response.put("message", "报警已解决");
            
            log.info("✅ 报警解决成功: alertId={}, type={}", alertId, alert.getAlertType());
            
        } catch (Exception e) {
            log.error("❌ 解决报警失败: alertId={}", alertId, e);
            response.put("success", false);
            response.put("message", "解决失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取报警详情
     */
    @GetMapping("/{alertId}")
    public Map<String, Object> getAlertDetail(@PathVariable Long alertId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("🔍 获取报警详情: alertId={}", alertId);
            
            AlertRecord alert = alertRecordMapper.selectById(alertId);
            if (alert == null) {
                response.put("success", false);
                response.put("message", "报警记录不存在");
                return response;
            }
            
            response.put("success", true);
            response.put("data", alert);
            
            log.info("✅ 获取报警详情成功: alertId={}, type={}", alertId, alert.getAlertType());
            
        } catch (Exception e) {
            log.error("❌ 获取报警详情失败: alertId={}", alertId, e);
            response.put("success", false);
            response.put("message", "获取详情失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取对应性检测报警列表
     */
    @GetMapping("/correspondence")
    public Map<String, Object> getCorrespondenceAlerts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("📋 查询对应性检测报警: page={}, size={}", page, size);

            QueryWrapper<AlertRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("alert_type", "CORRESPONDENCE_MISMATCH");
            wrapper.orderByDesc("created_time");

            // 分页查询
            Page<AlertRecord> pageResult = alertRecordMapper.selectPage(
                new Page<>(page, size), wrapper);

            response.put("success", true);
            response.put("data", pageResult.getRecords());
            response.put("total", pageResult.getTotal());
            response.put("pages", pageResult.getPages());
            response.put("current", pageResult.getCurrent());
            response.put("size", pageResult.getSize());

            log.info("✅ 查询对应性检测报警成功: 总数={}", pageResult.getTotal());

        } catch (Exception e) {
            log.error("❌ 查询对应性检测报警失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 处理对应性检测报警（专门针对车辆遮挡等情况）
     */
    @PostMapping("/{alertId}/handle-correspondence")
    public Map<String, Object> handleCorrespondenceAlert(
            @PathVariable Long alertId,
            @RequestParam String handlingType,
            @RequestParam(required = false) String comment,
            @RequestParam(required = false) Double adjustedThreshold) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("🔧 处理对应性报警: alertId={}, type={}, comment={}, threshold={}",
                alertId, handlingType, comment, adjustedThreshold);

            AlertRecord alert = alertRecordMapper.selectById(alertId);
            if (alert == null) {
                response.put("success", false);
                response.put("message", "报警记录不存在");
                return response;
            }

            if (!"CORRESPONDENCE_MISMATCH".equals(alert.getAlertType())) {
                response.put("success", false);
                response.put("message", "此接口仅用于处理对应性检测报警");
                return response;
            }

            // 更新报警详细信息
            try {
                AlertDetailData alertDetailData = alert.getAlertDetailData();

                alertDetailData.setHandlingTime(LocalDateTime.now());
                alertDetailData.setHandlingType(handlingType);
                alertDetailData.setHandlingComment(comment != null ? comment : "");

                if (adjustedThreshold != null) {
                    alertDetailData.setAdjustedThreshold(adjustedThreshold);
                }

                // 根据处理类型决定报警状态
                switch (handlingType) {
                    case "FALSE_POSITIVE":
                        // 误报：车辆遮挡、天气影响等
                        alert.setStatus("RESOLVED");
                        alertDetailData.setResolution("确认为误报，由外部因素导致");
                        break;
                    case "THRESHOLD_ADJUST":
                        // 阈值调整：当前阈值不合理
                        alert.setStatus("RESOLVED");
                        alertDetailData.setResolution("阈值调整，问题已解决");
                        break;
                    case "REAL_ISSUE":
                        // 真实问题：确实存在标牌对应性问题
                        alert.setStatus("ACKNOWLEDGED");
                        alertDetailData.setResolution("确认为真实问题，需要现场处理");
                        break;
                    case "NEED_INSPECTION":
                        // 需要现场检查
                        alert.setStatus("ACKNOWLEDGED");
                        alertDetailData.setResolution("需要现场人工检查确认");
                        break;
                    default:
                        alert.setStatus("ACKNOWLEDGED");
                        break;
                }

                alert.setAlertDetailData(alertDetailData);

                if ("RESOLVED".equals(alert.getStatus())) {
                    alert.setResolvedTime(LocalDateTime.now());
                }

            } catch (Exception e) {
                log.warn("更新报警详情失败: {}", e.getMessage());
            }

            alertRecordMapper.updateById(alert);

            response.put("success", true);
            response.put("message", "报警处理完成");
            response.put("data", alert);

            log.info("✅ 对应性报警处理成功: alertId={}, handlingType={}, status={}",
                alertId, handlingType, alert.getStatus());

        } catch (Exception e) {
            log.error("❌ 处理对应性报警失败: alertId={}", alertId, e);
            response.put("success", false);
            response.put("message", "处理失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 批量处理报警
     */
    @PostMapping("/batch-handle")
    public Map<String, Object> batchHandleAlerts(
            @RequestParam List<Long> alertIds,
            @RequestParam String action,
            @RequestParam(required = false) String reason) {

        Map<String, Object> response = new HashMap<>();

        try {
            log.info("📦 批量处理报警: alertIds={}, action={}, reason={}", alertIds, action, reason);

            int successCount = 0;
            int failCount = 0;

            for (Long alertId : alertIds) {
                try {
                    AlertRecord alert = alertRecordMapper.selectById(alertId);
                    if (alert != null) {
                        switch (action) {
                            case "acknowledge":
                                alert.setStatus("ACKNOWLEDGED");
                                break;
                            case "resolve":
                                alert.setStatus("RESOLVED");
                                alert.setResolvedTime(LocalDateTime.now());
                                break;
                        }

                        // 添加批量处理信息
                        try {
                            AlertDetailData alertDetailData = alert.getAlertDetailData();
                            alertDetailData.setBatchProcessTime(LocalDateTime.now());
                            alertDetailData.setBatchProcessReason(reason != null ? reason : "批量处理");
                            alert.setAlertDetailData(alertDetailData);
                        } catch (Exception e) {
                            log.warn("更新批量处理信息失败: {}", e.getMessage());
                        }

                        alertRecordMapper.updateById(alert);
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("批量处理单个报警失败: alertId={}", alertId, e);
                    failCount++;
                }
            }

            response.put("success", true);
            response.put("message", String.format("批量处理完成：成功%d个，失败%d个", successCount, failCount));
            response.put("successCount", successCount);
            response.put("failCount", failCount);

            log.info("✅ 批量处理报警完成: 成功={}, 失败={}", successCount, failCount);

        } catch (Exception e) {
            log.error("❌ 批量处理报警失败", e);
            response.put("success", false);
            response.put("message", "批量处理失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取类型化的报警详情
     * 返回结构化的报警数据，便于前端处理
     */
    @GetMapping("/{alertId}/detail")
    public Map<String, Object> getAlertDetailTyped(@PathVariable Long alertId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("🔍 获取类型化报警详情: alertId={}", alertId);

            AlertRecord alert = alertRecordMapper.selectById(alertId);
            if (alert == null) {
                response.put("success", false);
                response.put("message", "报警记录不存在");
                return response;
            }

            // 获取类型化的详细数据
            AlertDetailData detailData = alert.getAlertDetailData();

            // 构建响应数据
            Map<String, Object> alertInfo = new HashMap<>();
            alertInfo.put("id", alert.getId());
            alertInfo.put("alertType", alert.getAlertType());
            alertInfo.put("alertLevel", alert.getAlertLevel());
            alertInfo.put("alertLevelText", alert.getAlertLevelText());
            alertInfo.put("alertMessage", alert.getAlertMessage());
            alertInfo.put("status", alert.getStatus());
            alertInfo.put("statusText", alert.getStatusText());
            alertInfo.put("createdTime", alert.getCreatedTime());
            alertInfo.put("resolvedTime", alert.getResolvedTime());
            alertInfo.put("signImageUrl", alert.getSignImageUrl());
            alertInfo.put("groundImageUrl", alert.getGroundImageUrl());

            // 根据报警类型提供不同的详细信息
            Map<String, Object> typeSpecificData = new HashMap<>();

            if (alert.isCorrespondenceAlert()) {
                typeSpecificData.put("signName", detailData.getSignName());
                typeSpecificData.put("inspectionNumber", detailData.getInspectionNumber());
                typeSpecificData.put("matchingDescription", detailData.getMatchingDescription());
                typeSpecificData.put("completionRatePercent", detailData.getCompletionRatePercent());
                typeSpecificData.put("expectedLanes", detailData.getExpectedLanes());
                typeSpecificData.put("foundMarkings", detailData.getFoundMarkings());
                typeSpecificData.put("alertReasonText", detailData.getAlertReasonText());
            } else if (alert.isAssetMissingAlert()) {
                typeSpecificData.put("assetName", detailData.getAssetName());
                typeSpecificData.put("assetType", detailData.getAssetType());
                typeSpecificData.put("missingReason", detailData.getMissingReason());
                typeSpecificData.put("timeoutMinutes", detailData.getTimeoutMinutes());
            } else if (alert.isGpsAbnormalAlert()) {
                typeSpecificData.put("reason", detailData.getReason());
                typeSpecificData.put("thresholdMinutes", detailData.getThresholdMinutes());
            }

            alertInfo.put("typeSpecificData", typeSpecificData);
            alertInfo.put("locationDescription", detailData.getLocationDescription());

            response.put("success", true);
            response.put("data", alertInfo);
            response.put("detailData", detailData); // 完整的详细数据

            log.info("✅ 获取类型化报警详情成功: alertId={}, type={}", alertId, alert.getAlertType());

        } catch (Exception e) {
            log.error("❌ 获取类型化报警详情失败: alertId={}", alertId, e);
            response.put("success", false);
            response.put("message", "获取详情失败: " + e.getMessage());
        }

        return response;
    }
}
