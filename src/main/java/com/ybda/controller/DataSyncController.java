package com.ybda.controller;

import com.ybda.service.DataSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据同步控制器
 * 提供数据导出/导入的API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/sync")
@RequiredArgsConstructor
public class DataSyncController {
    
    private final DataSyncService dataSyncService;
    
    /**
     * 导出巡检数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param includeImages 是否包含图片
     * @return 导出结果
     */
    @PostMapping("/export")
    public ResponseEntity<Map<String, Object>> exportInspectionData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "true") boolean includeImages) {
        
        try {
            log.info("📤 接收导出请求: {} 到 {}, 包含图片: {}", startTime, endTime, includeImages);
            
            Map<String, Object> result = dataSyncService.exportInspectionData(startTime, endTime, includeImages);
            
            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("❌ 导出数据异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "导出失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 导入基础数据
     * 
     * @param dataFile 数据文件
     * @param imageFile 图片文件（可选）
     * @return 导入结果
     */
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importBaseData(
            @RequestParam("dataFile") MultipartFile dataFile,
            @RequestParam(value = "imageFile", required = false) MultipartFile imageFile) {
        
        try {
            log.info("📥 接收导入请求: 数据文件={}, 图片文件={}", 
                dataFile.getOriginalFilename(), 
                imageFile != null ? imageFile.getOriginalFilename() : "无");
            
            // 验证数据文件
            if (dataFile.isEmpty()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "数据文件不能为空");
                return ResponseEntity.badRequest().body(errorResult);
            }
            
            // 保存上传的文件
            String dataFilePath = saveUploadedFile(dataFile, "data");
            String imageFilePath = null;
            
            if (imageFile != null && !imageFile.isEmpty()) {
                imageFilePath = saveUploadedFile(imageFile, "images");
            }
            
            // 执行导入
            Map<String, Object> result = dataSyncService.importBaseData(dataFilePath, imageFilePath);
            
            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("❌ 导入数据异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "导入失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 获取待同步数据统计
     * 
     * @param lastSyncTime 上次同步时间（可选）
     * @return 统计结果
     */
    @GetMapping("/pending-stats")
    public ResponseEntity<Map<String, Object>> getPendingSyncStats(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime lastSyncTime) {
        
        try {
            log.info("📊 获取待同步数据统计: 上次同步时间={}", lastSyncTime);
            
            Map<String, Object> result = dataSyncService.getPendingSyncStats(lastSyncTime);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("❌ 获取统计数据异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取统计失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 检查网络连接状态
     * 
     * @return 网络状态
     */
    @GetMapping("/network-status")
    public ResponseEntity<Map<String, Object>> checkNetworkStatus() {
        
        try {
            log.info("🌐 检查网络连接状态");
            
            Map<String, Object> result = dataSyncService.checkNetworkStatus();
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("❌ 检查网络状态异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "检查失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 执行自动同步
     * 
     * @return 同步结果
     */
    @PostMapping("/auto-sync")
    public ResponseEntity<Map<String, Object>> performAutoSync() {
        
        try {
            log.info("🔄 执行自动同步");
            
            Map<String, Object> result = dataSyncService.performAutoSync();
            
            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("❌ 自动同步异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "自动同步失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 获取同步历史记录
     * 
     * @param limit 记录数量限制
     * @return 同步历史
     */
    @GetMapping("/history")
    public ResponseEntity<Map<String, Object>> getSyncHistory(
            @RequestParam(defaultValue = "20") int limit) {
        
        try {
            log.info("📋 获取同步历史: 限制{}条", limit);
            
            Map<String, Object> result = dataSyncService.getSyncHistory(limit);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("❌ 获取同步历史异常", e);
            
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "获取历史失败: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 保存上传的文件
     * 
     * @param file 上传的文件
     * @param type 文件类型（data/images）
     * @return 保存的文件路径
     */
    private String saveUploadedFile(MultipartFile file, String type) throws IOException {
        // 创建上传目录
        String uploadDir = "D:\\资产管理同步文件\\upload\\" + type + "\\";
        File directory = new File(uploadDir);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        
        // 生成文件名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null && originalFilename.contains(".") 
            ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
            : "";
        String fileName = type + "_" + timestamp + extension;
        
        // 保存文件
        File destFile = new File(directory, fileName);
        file.transferTo(destFile);
        
        log.info("📁 文件保存成功: {}", destFile.getAbsolutePath());
        return destFile.getAbsolutePath();
    }
}
