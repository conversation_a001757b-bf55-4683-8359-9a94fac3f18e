package com.ybda.controller;

import com.ybda.mapper.ImageReviewTaskMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.entity.ImageReviewTask;
import com.ybda.model.entity.TrafficAsset;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图片质量审核控制器
 * 处理资产图片质量的人工审核
 */
@Slf4j
@RestController
@RequestMapping("/api/image-quality-review")
public class ImageQualityReviewController {

    @Autowired
    private TrafficAssetMapper trafficAssetMapper;

    @Autowired
    private ImageReviewTaskMapper imageReviewTaskMapper;

    /**
     * 获取待审核的图片列表
     */
    @GetMapping("/pending")
    public Map<String, Object> getPendingReviews() {
        Map<String, Object> response = new HashMap<>();

        try {
            // 查询状态为待审核的资产
            List<TrafficAsset> pendingAssets = trafficAssetMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<TrafficAsset>()
                    .eq("image_quality_status", "待审核")
                    .isNotNull("detection_image_url")
                    .orderByDesc("created_time")
            );

            response.put("success", true);
            response.put("data", pendingAssets);
            response.put("count", pendingAssets.size());

            log.info("📋 获取待审核图片列表: {}个", pendingAssets.size());

        } catch (Exception e) {
            log.error("❌ 获取待审核图片列表失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 获取待审核的图片任务列表
     */
    @GetMapping("/pending-tasks")
    public Map<String, Object> getPendingTasks() {
        Map<String, Object> response = new HashMap<>();

        try {
            List<ImageReviewTask> pendingTasks = imageReviewTaskMapper.selectPendingTasks();

            response.put("success", true);
            response.put("data", pendingTasks);
            response.put("count", pendingTasks.size());

            log.info("📋 获取待审核图片任务列表: {}个", pendingTasks.size());

        } catch (Exception e) {
            log.error("❌ 获取待审核图片任务列表失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 审核图片质量
     */
    @PostMapping("/review/{assetId}")
    public Map<String, Object> reviewImageQuality(
            @PathVariable String assetId,
            @RequestParam String action, // APPROVE 或 REJECT
            @RequestParam(required = false) String reason,
            @RequestParam(required = false) String reviewerId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("🔍 处理图片质量审核: assetId={}, action={}, reason={}", assetId, action, reason);
            
            String status;
            if ("APPROVE".equals(action)) {
                status = "审核通过";
                log.info("✅ 图片质量审核通过: assetId={}", assetId);
            } else if ("REJECT".equals(action)) {
                status = "审核拒绝";
                log.info("❌ 图片质量审核拒绝: assetId={}, 原因={}", assetId, reason);
            } else {
                throw new IllegalArgumentException("无效的审核动作: " + action);
            }
            
            // 更新审核状态
            int updated = trafficAssetMapper.updateImageQualityStatus(
                assetId, 
                status, 
                reviewerId != null ? reviewerId : "system",
                LocalDateTime.now(),
                reason
            );
            
            if (updated > 0) {
                response.put("success", true);
                response.put("message", "审核完成");
                response.put("status", status);
            } else {
                response.put("success", false);
                response.put("message", "资产不存在或更新失败");
            }
            
        } catch (Exception e) {
            log.error("❌ 图片质量审核失败: assetId={}", assetId, e);
            response.put("success", false);
            response.put("message", "审核失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 批量审核图片质量
     */
    @PostMapping("/batch-review")
    public Map<String, Object> batchReviewImages(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, String>> reviews = (List<Map<String, String>>) request.get("reviews");
            String reviewerId = (String) request.get("reviewerId");
            
            int successCount = 0;
            int failCount = 0;
            
            for (Map<String, String> review : reviews) {
                try {
                    String assetId = review.get("assetId");
                    String action = review.get("action");
                    String reason = review.get("reason");
                    
                    String status = "APPROVE".equals(action) ? "审核通过" : "审核拒绝";
                    
                    int updated = trafficAssetMapper.updateImageQualityStatus(
                        assetId, status, reviewerId, LocalDateTime.now(), reason
                    );
                    
                    if (updated > 0) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                    
                } catch (Exception e) {
                    log.warn("⚠️ 批量审核单项失败: {}", review, e);
                    failCount++;
                }
            }
            
            response.put("success", true);
            response.put("message", "批量审核完成");
            response.put("successCount", successCount);
            response.put("failCount", failCount);
            
            log.info("📦 批量审核完成: 成功{}个, 失败{}个", successCount, failCount);
            
        } catch (Exception e) {
            log.error("❌ 批量审核失败", e);
            response.put("success", false);
            response.put("message", "批量审核失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取资产的图片审核历史
     */
    @GetMapping("/history/{assetId}")
    public Map<String, Object> getReviewHistory(@PathVariable String assetId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            TrafficAsset asset = trafficAssetMapper.selectByAssetId(assetId);
            
            if (asset != null) {
                Map<String, Object> history = new HashMap<>();
                history.put("assetId", asset.getAssetId());
                history.put("imageUrl", asset.getDetectionImageUrl());
                history.put("qualityStatus", asset.getImageQualityStatus());
                history.put("reviewerId", asset.getImageReviewerId());
                history.put("reviewTime", asset.getImageReviewTime());
                history.put("reviewReason", asset.getImageReviewReason());
                
                response.put("success", true);
                response.put("data", history);
            } else {
                response.put("success", false);
                response.put("message", "资产不存在");
            }
            
        } catch (Exception e) {
            log.error("❌ 获取审核历史失败: assetId={}", assetId, e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }
        
        return response;
    }
}
