package com.ybda.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.dto.DetectionRequestDTO;
import com.ybda.service.DetectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 检测数据接收控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/detection")
@RequiredArgsConstructor
@Validated
public class DetectionController {

    private final DetectionService detectionService;
    private final ObjectMapper objectMapper;
    
    /**
     * 接收检测数据和资产图片
     * 核心POST接口，必须同时传入检测结果JSON和对应的资产图片
     */
    @PostMapping(value = "/receive", consumes = {"multipart/form-data"})
    public ResponseEntity<Map<String, Object>> receiveDetection(
            @RequestParam("data") String detectionDataJson,
            @RequestParam("image") MultipartFile imageFile) {

        try {
            // 验证图片文件（必需）
            if (imageFile == null || imageFile.isEmpty()) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "资产图片是必需的");
                return ResponseEntity.badRequest().body(errorResult);
            }

            // 解析JSON数据
            DetectionRequestDTO detectionRequest = objectMapper.readValue(detectionDataJson, DetectionRequestDTO.class);

            log.info("📨 接收到检测请求: frameId={}, timestamp={}, signsCount={}",
                detectionRequest.getFrameId(),
                detectionRequest.getTimestamp(),
                detectionRequest.getSigns() != null ? detectionRequest.getSigns().size() : 0);

            // 打印检测到的标志详情
            if (detectionRequest.getSigns() != null && !detectionRequest.getSigns().isEmpty()) {
                log.info("🏷️ 检测到的标志:");
                for (int i = 0; i < detectionRequest.getSigns().size(); i++) {
                    var sign = detectionRequest.getSigns().get(i);
                    log.info("  [{}] type={}, name={}, classId={}, trackId={}",
                        i+1, sign.getType(), sign.getName(), sign.getClassId(), sign.getTrackId());
                }
            }

            // 处理资产图片并建立对应关系
            String imageUrl = handleAssetImageUpload(imageFile, detectionRequest);
            if (imageUrl != null) {
                detectionRequest.setImageUrl(imageUrl);
                log.info("📷 资产图片保存成功: {}", imageUrl);
            } else {
                log.info("⚠️ 图片保存失败，但继续处理检测数据");
            }

            // 处理检测请求
            Map<String, Object> result = detectionService.processDetectionRequest(detectionRequest);

            if ((Boolean) result.get("success")) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }

        } catch (Exception e) {
            log.error("❌ 处理检测请求异常: frameId={}",
                detectionDataJson.contains("frameId") ? "解析失败" : "未知", e);

            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "服务器内部错误: " + e.getMessage());

            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 处理资产图片上传并建立与检测资产的对应关系
     * @param imageFile 图片文件
     * @param detectionRequest 检测请求数据
     * @return 图片URL
     */
    private String handleAssetImageUpload(MultipartFile imageFile, DetectionRequestDTO detectionRequest) {
        try {
            // 验证文件类型
            String contentType = imageFile.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                log.info("⚠️ 无效的图片文件类型: {}", contentType);
                return null;
            }

            // 验证文件大小（限制10MB）
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (imageFile.getSize() > maxSize) {
                log.info("⚠️ 图片文件过大: {}KB, 限制: {}KB",
                    imageFile.getSize() / 1024, maxSize / 1024);
                return null;
            }

            // 生成基于资产信息的文件名
            String fileName = generateAssetImageFileName(imageFile, detectionRequest);

            // 保存图片文件
            String imageUrl = saveAssetImage(imageFile, fileName);

            log.info("📷 资产图片上传成功: fileName={}, size={}KB, detectedAssets={}",
                fileName, imageFile.getSize() / 1024,
                detectionRequest.getSigns() != null ? detectionRequest.getSigns().size() : 0);

            return imageUrl;

        } catch (Exception e) {
            log.error("❌ 资产图片上传失败: frameId={}", detectionRequest.getFrameId(), e);
            return null;
        }
    }

    /**
     * 生成资产图片文件名
     * 格式: frameId_timestamp_assetTypes.jpg
     */
    private String generateAssetImageFileName(MultipartFile imageFile, DetectionRequestDTO detectionRequest) {
        // 获取文件扩展名
        String originalFilename = imageFile.getOriginalFilename();
        String fileExtension = ".jpg"; // 默认扩展名
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }

        // 生成资产类型标识
        StringBuilder assetTypes = new StringBuilder();
        if (detectionRequest.getSigns() != null && !detectionRequest.getSigns().isEmpty()) {
            for (int i = 0; i < detectionRequest.getSigns().size(); i++) {
                if (i > 0) assetTypes.append("-");
                String type = detectionRequest.getSigns().get(i).getType();
                // 简化类型名称
                switch (type) {
                    case "ground_marking" -> assetTypes.append("GM");
                    case "overhead_sign" -> assetTypes.append("OS");
                    case "traffic_light" -> assetTypes.append("TL");
                    case "barrier" -> assetTypes.append("BR");
                    default -> assetTypes.append("AS");
                }
            }
        } else {
            assetTypes.append("UNKNOWN");
        }

        return String.format("%s_%d_%s%s",
            detectionRequest.getFrameId(),
            detectionRequest.getTimestamp(),
                assetTypes,
            fileExtension);
    }

    /**
     * 保存资产图片到本地文件系统
     * @param imageFile 图片文件
     * @param fileName 文件名
     * @return 图片访问URL
     */
    private String saveAssetImage(MultipartFile imageFile, String fileName) throws IOException {
        // 创建按日期分组的目录结构
        String dateDir = java.time.LocalDate.now().toString(); // 2024-01-15
        String uploadDir = "D:\\资产管理图片文件\\" + dateDir + "\\";

        File directory = new File(uploadDir);
        if (!directory.exists()) {
            directory.mkdirs();
            log.debug("📁 创建目录: {}", uploadDir);
        }

        // 保存文件
        File destFile = new File(directory, fileName);
        imageFile.transferTo(destFile);

        // 返回访问URL
        String imageUrl = "/assets/" + dateDir + "/" + fileName;
        log.debug("💾 图片保存路径: {}", destFile.getAbsolutePath());

        return imageUrl;
    }
}
