package com.ybda.service;

import java.util.Map;

/**
 * 数据导入服务接口
 * 负责从文件导入基础数据到系统
 */
public interface DataImportService {
    
    /**
     * 导入资产数据
     * 
     * @param dataFilePath JSON数据文件路径
     * @return 导入结果
     */
    Map<String, Object> importAssetData(String dataFilePath);
    
    /**
     * 导入系统配置
     * 
     * @param configFilePath 配置文件路径
     * @return 导入结果
     */
    Map<String, Object> importSystemConfig(String configFilePath);
    
    /**
     * 导入验证规则
     * 
     * @param rulesFilePath 规则文件路径
     * @return 导入结果
     */
    Map<String, Object> importVerificationRules(String rulesFilePath);
    
    /**
     * 解压并导入图片文件
     * 
     * @param zipFilePath ZIP文件路径
     * @param targetDir 目标目录
     * @return 导入结果
     */
    Map<String, Object> importImages(String zipFilePath, String targetDir);
    
    /**
     * 验证导入数据的完整性
     * 
     * @param dataFilePath 数据文件路径
     * @return 验证结果
     */
    Map<String, Object> validateImportData(String dataFilePath);
    
    /**
     * 执行完整的数据导入
     * 
     * @param packagePath 导入包路径
     * @return 导入结果
     */
    Map<String, Object> performFullImport(String packagePath);
}
