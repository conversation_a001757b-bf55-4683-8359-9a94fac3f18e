package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.model.entity.DetectionDetail;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.service.AssetInspectionService;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.utils.GeometryUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 资产巡检服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AssetInspectionServiceImpl implements AssetInspectionService {
    
    private final InspectionRecordMapper inspectionRecordMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final SignGroundMatchingService signGroundMatchingService;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final double MATCHING_DISTANCE = 30.0; // 30米范围内认为是同一位置（提高精度）
    private static final int DEFAULT_RECENT_COUNT = 10; // 默认分析最近10次巡检
    private static final double MISSING_THRESHOLD = 0.2; // 检测率低于20%认为可能缺失
    private static final double CONFIRMED_MISSING_THRESHOLD = 0.1; // 检测率低于10%确认缺失
    
    @Override
    public void createExistenceInspection(TrafficAsset asset, DetectionRecord detectionRecord, boolean detected) {
        try {
            // 检查是否已存在近期的巡检记录（避免重复创建）
            if (hasRecentInspection(asset.getAssetId())) {
                log.debug("🔍 资产已有近期巡检记录，跳过创建: assetId={}", asset.getAssetId());
                return;
            }

            // 获取下一个巡检次数
            Integer nextNumber = inspectionRecordMapper.getNextInspectionNumberByAsset(asset.getAssetId());
            if (nextNumber == null) nextNumber = 1;

            // 创建巡检记录
            InspectionRecord inspection = new InspectionRecord();
            inspection.setAssetId(asset.getAssetId());
            inspection.setInspectionType("存在性");
            inspection.setInspectionNumber(nextNumber);
            inspection.setInspectionTime(LocalDateTime.now());
            inspection.setDetected(detected);
            inspection.setExpectedMarkingsCount(1); // 存在性巡检期望检测到1个资产
            inspection.setFoundMarkingsCount(detected ? 1 : 0);
            
            if (detected && detectionRecord != null) {
                inspection.setDetectionRecordId(detectionRecord.getId());
                inspection.setGpsLatitude(detectionRecord.getGpsLatitude());
                inspection.setGpsLongitude(detectionRecord.getGpsLongitude());
            } else {
                // 未检测到时，使用资产的位置信息
                Double[] coordinates = getAssetRepresentativeCoordinates(asset);
                inspection.setGpsLatitude(coordinates[0]);
                inspection.setGpsLongitude(coordinates[1]);
            }
            
            inspection.calculateCompletionRate();
            inspection.setFinished(true); // 存在性巡检立即完成
            inspection.setCreatedTime(LocalDateTime.now());
            
            inspectionRecordMapper.insert(inspection);
            
            log.info("📋 创建资产存在性巡检记录: assetId={}, 第{}次巡检, 检测结果={}", 
                asset.getAssetId(), nextNumber, detected ? "检测到" : "未检测到");
                
        } catch (Exception e) {
            log.error("❌ 创建资产存在性巡检记录失败: assetId={}", asset.getAssetId(), e);
        }
    }
    
    @Override
    public void processAssetExistenceInspections(DetectionRecord detectionRecord) {
        try {
            if (!detectionRecord.hasValidGpsLocation()) {
                return;
            }
            
            // 查找附近的所有资产（包括点状和线状资产）
            List<TrafficAsset> nearbyAssets = findAllNearbyAssets(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE
            );
            
            if (nearbyAssets.isEmpty()) {
                log.debug("📍 附近没有资产需要巡检: location=({}, {})", 
                    detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
                return;
            }
            
            // 获取本次检测到的资产详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            log.info("🔍 开始处理资产存在性巡检: 附近资产{}个, 检测到{}个", 
                nearbyAssets.size(), detectionDetails.size());
            
            // 为每个附近的资产创建巡检记录
            for (TrafficAsset asset : nearbyAssets) {
                // 检查是否检测到该资产
                boolean detected = detectionDetails.stream()
                    .anyMatch(detail ->
                        asset.getType().equals(detail.getType()) &&
                        isAssetNameMatching(asset.getName(), detail.getName())
                    );

                // 🏷️ 优化：如果是天空标牌且检测到了，执行标牌地面匹配逻辑
                if (detected && "overhead_sign".equals(asset.getType())) {
                    try {
                        signGroundMatchingService.processOverheadSignDetection(detectionRecord, asset);
                        log.debug("🏷️ 处理天空标牌匹配: assetId={}, name={}", asset.getAssetId(), asset.getName());
                    } catch (Exception e) {
                        log.error("❌ 处理天空标牌匹配失败: assetId={}, name={}", asset.getAssetId(), asset.getName(), e);
                    }
                }

                // 创建巡检记录
                createExistenceInspection(asset, detectionRecord, detected);

                // 如果检测到了，更新资产的最后检测时间
                if (detected) {
                    trafficAssetMapper.updateDetectionInfo(
                        asset.getAssetId(),
                        detectionRecord.getCreatedTime(),
                        asset.getDetectionCount() + 1
                    );
                }
            }
            
            log.info("✅ 完成资产存在性巡检处理: 共处理{}个资产", nearbyAssets.size());
            
        } catch (Exception e) {
            log.error("❌ 处理资产存在性巡检失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    @Override
    public double analyzeAssetExistenceProbability(String assetId, int recentCount) {
        try {
            List<InspectionRecord> recentInspections = inspectionRecordMapper
                .selectLatestByAssetId(assetId, recentCount);
            
            if (recentInspections.isEmpty()) {
                return 1.0; // 没有巡检记录，假设存在
            }
            
            long detectedCount = recentInspections.stream()
                .filter(InspectionRecord::isDetected)
                .count();
            
            double probability = (double) detectedCount / recentInspections.size();
            
            log.debug("📊 资产存在概率分析: assetId={}, 最近{}次巡检, 检测到{}次, 概率={:.2f}", 
                assetId, recentInspections.size(), detectedCount, probability);
            
            return probability;
            
        } catch (Exception e) {
            log.error("❌ 分析资产存在概率失败: assetId={}", assetId, e);
            return 1.0; // 出错时假设存在
        }
    }
    
    @Override
    public void updateAssetStatusBasedOnInspections(String assetId) {
        try {
            double probability = analyzeAssetExistenceProbability(assetId, DEFAULT_RECENT_COUNT);
            
            String newStatus;
            if (probability >= 0.8) {
                newStatus = "正常";
            } else if (probability >= MISSING_THRESHOLD) {
                newStatus = "疑似缺失";
            } else {
                newStatus = "缺失";
            }
            
            // 更新资产状态
            trafficAssetMapper.updateStatus(assetId, newStatus);
            
            log.info("📈 基于巡检历史更新资产状态: assetId={}, 检测概率={:.2f}, 新状态={}", 
                assetId, probability, newStatus);
                
        } catch (Exception e) {
            log.error("❌ 更新资产状态失败: assetId={}", assetId, e);
        }
    }

    /**
     * 查找附近的所有资产（包括点状和线状资产）
     */
    private List<TrafficAsset> findAllNearbyAssets(Double latitude, Double longitude, Double distance) {
        List<TrafficAsset> allNearbyAssets = new ArrayList<>();

        try {
            CoordinatePoint gpsPoint = new CoordinatePoint(latitude, longitude);

            // 1. 查找点状资产（使用数据库空间查询，效率高）
            List<TrafficAsset> pointAssets = trafficAssetMapper.findNearbyPointAssets(
                latitude, longitude, distance);
            allNearbyAssets.addAll(pointAssets);

            // 2. 查找线状和面状资产（需要复杂几何计算）
            List<TrafficAsset> complexAssets = trafficAssetMapper.findComplexGeometryAssets();

            // 对线状和面状资产进行几何判断
            for (TrafficAsset asset : complexAssets) {
                if (GeometryUtils.isPointNearAsset(gpsPoint, asset, distance)) {
                    allNearbyAssets.add(asset);
                }
            }

            log.debug("🔍 查找附近资产: 点状{}个, 复杂几何{}个, 总计{}个",
                pointAssets.size(),
                allNearbyAssets.size() - pointAssets.size(),
                allNearbyAssets.size());

        } catch (Exception e) {
            log.error("❌ 查找附近资产失败: location=({}, {})", latitude, longitude, e);
        }

        return allNearbyAssets;
    }

    /**
     * 获取资产的代表性坐标（用于巡检记录）
     * 对于点状资产，返回其latitude/longitude
     * 对于线状/面状资产，从geometry_coordinates中提取第一个坐标点
     */
    private Double[] getAssetRepresentativeCoordinates(TrafficAsset asset) {
        try {
            // 优先使用点状坐标
            if (asset.getLatitude() != null && asset.getLongitude() != null) {
                return new Double[]{asset.getLatitude(), asset.getLongitude()};
            }

            // 对于线状/面状资产，从geometry_coordinates中提取坐标
            if (asset.getGeometryCoordinates() != null && !asset.getGeometryCoordinates().trim().isEmpty()) {
                List<CoordinatePoint> points = objectMapper.readValue(
                    asset.getGeometryCoordinates(),
                        new TypeReference<>() {
                        }
                );

                if (!points.isEmpty()) {
                    CoordinatePoint firstPoint = points.get(0);
                    log.info("📍 从线状资产几何坐标中提取代表性坐标: assetId={}, 坐标=({}, {})",
                        asset.getAssetId(), firstPoint.getLat(), firstPoint.getLng());
                    return new Double[]{firstPoint.getLat(), firstPoint.getLng()};
                }
            }

            // 如果都没有，返回默认坐标
            log.warn("⚠️ 资产缺少坐标信息，使用默认坐标: assetId={}", asset.getAssetId());
            return new Double[]{0.0, 0.0};

        } catch (Exception e) {
            log.error("❌ 提取资产代表性坐标失败: assetId={}", asset.getAssetId(), e);
            return new Double[]{0.0, 0.0};
        }
    }

    @Override
    public void cleanupOldInspectionRecords(int keepCount) {
        try {
            // 获取所有有巡检记录的资产
            List<String> assetIds = inspectionRecordMapper.selectByInspectionType("EXISTENCE")
                .stream()
                .map(InspectionRecord::getAssetId)
                .distinct()
                .toList();
            
            int cleanedCount = 0;
            for (String assetId : assetIds) {
                int deleted = inspectionRecordMapper.deleteOldAssetRecords(assetId, keepCount);
                cleanedCount += deleted;
            }
            
            log.info("🧹 清理旧巡检记录完成: 共清理{}条记录, 每个资产保留最新{}条", cleanedCount, keepCount);
            
        } catch (Exception e) {
            log.error("❌ 清理旧巡检记录失败", e);
        }
    }
    

    
    /**
     * 检查资产名称是否匹配
     */
    private boolean isAssetNameMatching(String assetName, String detectedName) {
        if (assetName == null || detectedName == null) {
            return false;
        }
        
        // 精确匹配
        if (assetName.equals(detectedName)) {
            return true;
        }
        
        // 模糊匹配（包含关系）
        return assetName.contains(detectedName) || detectedName.contains(assetName);
    }

    /**
     * 检查是否已存在近期的巡检记录
     * 避免在短时间内为同一个资产重复创建巡检记录
     */
    private boolean hasRecentInspection(String assetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusSeconds(10); // 10秒内
            LocalDateTime newAssetThreshold = LocalDateTime.now().minusSeconds(7); // 7秒阈值

            // 查询近期是否已有该资产的巡检记录
            List<InspectionRecord> recentInspections = inspectionRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<InspectionRecord>()
                    .eq("asset_id", assetId)
                    .eq("inspection_type", "EXISTENCE")
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            if (recentInspections.isEmpty()) {
                return false; // 没有近期记录
            }

            InspectionRecord recentInspection = recentInspections.get(0);

            // 计算时间差（秒）
            long secondsDiff = java.time.Duration.between(recentInspection.getCreatedTime(), LocalDateTime.now()).getSeconds();

            if (secondsDiff > 7) {
                // 超过7秒，认为是新资产，不合并
                log.debug("🆕 超过7秒阈值，认为是新资产: assetId={}, 时间差={}秒", assetId, secondsDiff);
                return false;
            } else {
                // 7秒内，认为是同一资产的重复检测，合并
                log.debug("🔄 7秒内重复检测，合并处理: assetId={}, 时间差={}秒", assetId, secondsDiff);
                return true;
            }

        } catch (Exception e) {
            log.warn("⚠️ 检查近期巡检记录失败: assetId={}", assetId, e);
            return false; // 出错时允许创建，避免影响正常流程
        }
    }
}
