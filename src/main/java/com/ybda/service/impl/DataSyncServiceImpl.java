package com.ybda.service.impl;

import com.ybda.mapper.SyncRecordMapper;
import com.ybda.model.entity.SyncRecord;
import com.ybda.service.DataExportService;
import com.ybda.service.DataImportService;
import com.ybda.service.DataSyncService;
import com.ybda.utils.RedisUtils;
import com.ybda.utils.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据同步服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSyncServiceImpl implements DataSyncService {
    
    private final DataExportService dataExportService;
    private final DataImportService dataImportService;
    private final SyncRecordMapper syncRecordMapper;
    private final RedisUtils redisUtils;
    
    // 配置参数
    private static final String SYNC_OUTPUT_DIR = "D:\\资产管理同步文件\\";
    private static final String REDIS_HOST = "***************";
    private static final int REDIS_PORT = 16379;
    private static final int NETWORK_TIMEOUT = 5000; // 5秒超时
    
    @Override
    @Transactional
    public Map<String, Object> exportInspectionData(LocalDateTime startTime, LocalDateTime endTime, boolean includeImages) {
        Map<String, Object> result = new HashMap<>();
        SyncRecord syncRecord = null;
        
        try {
            log.info("🔄 开始导出巡检数据: {} 到 {}, 包含图片: {}", startTime, endTime, includeImages);
            
            // 创建同步记录
            syncRecord = createSyncRecord("EXPORT", "TO_SERVER", "INSPECTION_DATA", startTime, endTime);
            syncRecord.setSyncStatus("IN_PROGRESS");
            syncRecordMapper.insert(syncRecord);

            // 创建输出目录
            String timestamp = TimeUtils.nowFilename();
            String outputDir = SYNC_OUTPUT_DIR + "export_" + timestamp + "\\";
            Files.createDirectories(Paths.get(outputDir));
            
            // 生成导出包
            Map<String, Object> exportResult = dataExportService.generateExportPackage(
                startTime, endTime, includeImages, outputDir);
            
            if ((Boolean) exportResult.get("success")) {
                // 更新同步记录
                syncRecord.setSyncStatus("SUCCESS");
                syncRecord.setEndTime(TimeUtils.now());
                syncRecord.setFilePath(outputDir);
                
                // 获取统计信息
                Map<String, Object> statistics = (Map<String, Object>) exportResult.get("statistics");
                if (statistics != null) {
                    Integer totalRecords = (Integer) statistics.get("totalRecords");
                    syncRecord.setRecordCount(totalRecords);
                    syncRecord.setSuccessCount(totalRecords);
                }
                
                // 计算文件大小
                try {
                    long totalSize = Files.walk(Paths.get(outputDir))
                        .filter(Files::isRegularFile)
                        .mapToLong(path -> {
                            try {
                                return Files.size(path);
                            } catch (IOException e) {
                                return 0;
                            }
                        })
                        .sum();
                    syncRecord.setFileSize(totalSize);
                } catch (Exception e) {
                    log.warn("⚠️ 计算文件大小失败", e);
                }
                
                // 创建汇总信息
                Map<String, Object> summary = new HashMap<>();
                summary.put("outputDir", outputDir);
                summary.put("dataFile", exportResult.get("dataFile"));
                summary.put("imageFile", exportResult.get("imageFile"));
                summary.put("totalRecords", syncRecord.getRecordCount());
                summary.put("fileSize", syncRecord.getFileSize());
                summary.put("exportTime", TimeUtils.formatStandard(TimeUtils.now()));
                summary.put("dataTimeRange", Map.of(
                    "startTime", TimeUtils.formatStandard(startTime),
                    "endTime", TimeUtils.formatStandard(endTime)
                ));

                result.put("success", true);
                result.put("message", "巡检数据导出成功");
                result.put("summary", summary);
                
                log.info("✅ 巡检数据导出成功: {}", outputDir);
                
            } else {
                syncRecord.setSyncStatus("FAILED");
                syncRecord.setEndTime(TimeUtils.now());
                syncRecord.setErrorMessage((String) exportResult.get("message"));
                
                result.put("success", false);
                result.put("message", "导出失败: " + exportResult.get("message"));
            }
            
        } catch (Exception e) {
            log.error("❌ 导出巡检数据失败", e);
            
            if (syncRecord != null) {
                syncRecord.setSyncStatus("FAILED");
                syncRecord.setEndTime(TimeUtils.now());
                syncRecord.setErrorMessage(e.getMessage());
            }
            
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        } finally {
            if (syncRecord != null) {
                syncRecord.setUpdatedTime(TimeUtils.now());
                syncRecordMapper.updateById(syncRecord);
            }
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> importBaseData(String dataFilePath, String imageFilePath) {
        Map<String, Object> result = new HashMap<>();
        SyncRecord syncRecord = null;
        
        try {
            log.info("🔄 开始导入基础数据: 数据文件={}, 图片文件={}", dataFilePath, imageFilePath);
            
            // 创建同步记录
            syncRecord = createSyncRecord("IMPORT", "FROM_SERVER", "BASE_DATA", null, null);
            syncRecord.setSyncStatus("IN_PROGRESS");
            syncRecord.setFilePath(dataFilePath);
            syncRecordMapper.insert(syncRecord);
            
            Map<String, Object> importResults = new HashMap<>();
            
            // 导入数据文件
            if (dataFilePath != null && Files.exists(Paths.get(dataFilePath))) {
                // 验证数据
                Map<String, Object> validationResult = dataImportService.validateImportData(dataFilePath);
                if (!(Boolean) validationResult.get("success")) {
                    syncRecord.setSyncStatus("FAILED");
                    syncRecord.setErrorMessage("数据验证失败: " + validationResult.get("message"));
                    
                    result.put("success", false);
                    result.put("message", "数据验证失败");
                    result.put("validationResult", validationResult);
                    return result;
                }
                
                // 导入资产数据
                Map<String, Object> assetResult = dataImportService.importAssetData(dataFilePath);
                importResults.put("assets", assetResult);
                
                if ((Boolean) assetResult.get("success")) {
                    syncRecord.setRecordCount((Integer) assetResult.get("totalCount"));
                    syncRecord.setSuccessCount((Integer) assetResult.get("successCount"));
                    syncRecord.setFailedCount((Integer) assetResult.get("failedCount"));
                }
            }
            
            // 导入图片文件
            if (imageFilePath != null && Files.exists(Paths.get(imageFilePath))) {
                String imageTargetDir = "D:\\资产管理图片文件\\imported\\";
                Map<String, Object> imageResult = dataImportService.importImages(imageFilePath, imageTargetDir);
                importResults.put("images", imageResult);
            }
            
            // 判断整体导入结果
            boolean overallSuccess = importResults.values().stream()
                .allMatch(resultMap -> (Boolean) ((Map<String, Object>) resultMap).get("success"));
            
            if (overallSuccess) {
                syncRecord.setSyncStatus("SUCCESS");
                result.put("success", true);
                result.put("message", "基础数据导入成功");
                log.info("✅ 基础数据导入成功");
            } else {
                syncRecord.setSyncStatus("FAILED");
                syncRecord.setErrorMessage("部分导入失败");
                result.put("success", false);
                result.put("message", "部分导入失败");
            }
            
            syncRecord.setEndTime(LocalDateTime.now());
            result.put("importResults", importResults);
            
        } catch (Exception e) {
            log.error("❌ 导入基础数据失败", e);
            
            if (syncRecord != null) {
                syncRecord.setSyncStatus("FAILED");
                syncRecord.setEndTime(LocalDateTime.now());
                syncRecord.setErrorMessage(e.getMessage());
            }
            
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        } finally {
            if (syncRecord != null) {
                syncRecord.setUpdatedTime(LocalDateTime.now());
                syncRecordMapper.updateById(syncRecord);
            }
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> getPendingSyncStats(LocalDateTime lastSyncTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 获取待同步数据统计: 上次同步时间={}", lastSyncTime);
            
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = lastSyncTime != null ? lastSyncTime : now.minusDays(1);
            
            // 获取各类数据的统计
            Map<String, Object> detectionStats = dataExportService.exportDetectionRecords(startTime, now);
            Map<String, Object> inspectionStats = dataExportService.exportInspectionRecords(startTime, now);
            Map<String, Object> assetStats = dataExportService.exportNewAssets(startTime, now);
            Map<String, Object> alertStats = dataExportService.exportAlertRecords(startTime, now);
            
            Map<String, Integer> stats = new HashMap<>();
            stats.put("detectionRecords", (Integer) detectionStats.get("count"));
            stats.put("inspectionRecords", (Integer) inspectionStats.get("count"));
            stats.put("newAssets", (Integer) assetStats.get("count"));
            stats.put("alertRecords", (Integer) alertStats.get("count"));
            
            int totalPending = stats.values().stream().mapToInt(Integer::intValue).sum();
            
            result.put("success", true);
            result.put("lastSyncTime", lastSyncTime);
            result.put("currentTime", now);
            result.put("totalPending", totalPending);
            result.put("details", stats);
            result.put("message", "待同步数据统计获取成功");
            
            log.info("✅ 待同步数据统计: 总计{}条记录", totalPending);
            
        } catch (Exception e) {
            log.error("❌ 获取待同步数据统计失败", e);
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> checkNetworkStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 检查网络连接状态");
            
            boolean redisConnected = checkRedisConnection();
            boolean internetConnected = checkInternetConnection();
            
            result.put("success", true);
            result.put("redisConnected", redisConnected);
            result.put("internetConnected", internetConnected);
            result.put("overallConnected", redisConnected && internetConnected);
            result.put("checkTime", LocalDateTime.now());
            result.put("message", "网络状态检查完成");
            
            log.info("✅ 网络状态: Redis={}, Internet={}", redisConnected, internetConnected);
            
        } catch (Exception e) {
            log.error("❌ 检查网络状态失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> performAutoSync() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 执行自动同步");
            
            // 检查网络状态
            Map<String, Object> networkStatus = checkNetworkStatus();
            if (!(Boolean) networkStatus.get("overallConnected")) {
                result.put("success", false);
                result.put("message", "网络连接不可用，跳过自动同步");
                result.put("networkStatus", networkStatus);
                return result;
            }
            
            // 获取上次同步时间
            LocalDateTime lastSyncTime = syncRecordMapper.findLastSuccessfulSyncTime("EXPORT", "TO_SERVER");
            if (lastSyncTime == null) {
                lastSyncTime = LocalDateTime.now().minusDays(1); // 默认同步最近1天的数据
            }
            
            // 检查是否有待同步的数据
            Map<String, Object> pendingStats = getPendingSyncStats(lastSyncTime);
            int totalPending = (Integer) pendingStats.get("totalPending");
            
            if (totalPending == 0) {
                result.put("success", true);
                result.put("message", "没有待同步的数据");
                result.put("pendingStats", pendingStats);
                return result;
            }
            
            // 执行导出
            LocalDateTime now = LocalDateTime.now();
            Map<String, Object> exportResult = exportInspectionData(lastSyncTime, now, true);
            
            result.put("success", (Boolean) exportResult.get("success"));
            result.put("message", "自动同步完成");
            result.put("exportResult", exportResult);
            result.put("pendingStats", pendingStats);
            result.put("networkStatus", networkStatus);
            
            log.info("✅ 自动同步完成: 成功={}", result.get("success"));
            
        } catch (Exception e) {
            log.error("❌ 执行自动同步失败", e);
            result.put("success", false);
            result.put("message", "自动同步失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> getSyncHistory(int limit) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 获取同步历史记录: 限制{}条", limit);
            
            List<SyncRecord> exportRecords = syncRecordMapper.findRecentSyncRecords("EXPORT", "TO_SERVER", limit);
            List<SyncRecord> importRecords = syncRecordMapper.findRecentSyncRecords("IMPORT", "FROM_SERVER", limit);
            
            result.put("success", true);
            result.put("exportRecords", exportRecords);
            result.put("importRecords", importRecords);
            result.put("message", "同步历史获取成功");
            
            log.info("✅ 同步历史获取成功: 导出{}条, 导入{}条", exportRecords.size(), importRecords.size());
            
        } catch (Exception e) {
            log.error("❌ 获取同步历史失败", e);
            result.put("success", false);
            result.put("message", "获取失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 创建同步记录
     */
    private SyncRecord createSyncRecord(String syncType, String syncDirection, String dataType,
                                      LocalDateTime dataStartTime, LocalDateTime dataEndTime) {
        SyncRecord record = new SyncRecord();
        record.setSyncType(syncType);
        record.setSyncDirection(syncDirection);
        record.setDataType(dataType);
        record.setStartTime(TimeUtils.now());
        record.setDataStartTime(dataStartTime);
        record.setDataEndTime(dataEndTime);
        record.setCreatedTime(TimeUtils.now());
        record.setUpdatedTime(TimeUtils.now());
        return record;
    }
    
    /**
     * 检查Redis连接
     */
    private boolean checkRedisConnection() {
        try {
            redisUtils.set("network_test", "test");
            String value = redisUtils.get("network_test");
            redisUtils.delete("network_test");
            return "test".equals(value);
        } catch (Exception e) {
            log.debug("Redis连接检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查互联网连接
     */
    private boolean checkInternetConnection() {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(REDIS_HOST, REDIS_PORT), NETWORK_TIMEOUT);
            return true;
        } catch (Exception e) {
            log.debug("互联网连接检查失败: {}", e.getMessage());
            return false;
        }
    }
}
