package com.ybda.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.*;
import com.ybda.model.entity.*;
import com.ybda.service.DataExportService;
import com.ybda.utils.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 数据导出服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataExportServiceImpl implements DataExportService {
    
    private final DetectionRecordMapper detectionRecordMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final InspectionRecordMapper inspectionRecordMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final ObjectMapper objectMapper;
    
    @Override
    public Map<String, Object> exportDetectionRecords(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导出检测记录: {} 到 {}", startTime, endTime);

            // 查询检测记录
            QueryWrapper<DetectionRecord> wrapper = new QueryWrapper<>();
            wrapper.between("created_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            log.info("🔍 查询条件: created_time BETWEEN '{}' AND '{}'",
                TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            List<DetectionRecord> records = detectionRecordMapper.selectList(wrapper);
            log.info("📊 查询到检测记录数量: {}", records.size());
            
            // 转换为Map格式便于JSON序列化
            List<Map<String, Object>> recordMaps = new ArrayList<>();
            for (DetectionRecord record : records) {
                Map<String, Object> recordMap = convertToMap(record);
                
                // 查询关联的检测详情
                QueryWrapper<DetectionDetail> detailWrapper = new QueryWrapper<>();
                detailWrapper.eq("detection_record_id", record.getId());
                List<DetectionDetail> details = detectionDetailMapper.selectList(detailWrapper);
                recordMap.put("details", details);
                
                recordMaps.add(recordMap);
            }
            
            result.put("success", true);
            result.put("data", recordMaps);
            result.put("count", recordMaps.size());
            result.put("message", "检测记录导出成功");
            
            log.info("✅ 检测记录导出完成: 共{}条记录", recordMaps.size());
            
        } catch (Exception e) {
            log.error("❌ 导出检测记录失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> exportInspectionRecords(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导出巡检记录: {} 到 {}", startTime, endTime);

            QueryWrapper<InspectionRecord> wrapper = new QueryWrapper<>();
            wrapper.between("inspection_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            log.info("🔍 查询条件: inspection_time BETWEEN '{}' AND '{}'",
                TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            List<InspectionRecord> records = inspectionRecordMapper.selectList(wrapper);
            log.info("📊 查询到巡检记录数量: {}", records.size());
            
            List<Map<String, Object>> recordMaps = new ArrayList<>();
            for (InspectionRecord record : records) {
                recordMaps.add(convertToMap(record));
            }
            
            result.put("success", true);
            result.put("data", recordMaps);
            result.put("count", recordMaps.size());
            result.put("message", "巡检记录导出成功");
            
            log.info("✅ 巡检记录导出完成: 共{}条记录", recordMaps.size());
            
        } catch (Exception e) {
            log.error("❌ 导出巡检记录失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> exportNewAssets(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导出新增资产: {} 到 {}", startTime, endTime);

            QueryWrapper<TrafficAsset> wrapper = new QueryWrapper<>();
            wrapper.between("created_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            log.info("🔍 查询条件: created_time BETWEEN '{}' AND '{}'",
                TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            List<TrafficAsset> assets = trafficAssetMapper.selectList(wrapper);
            log.info("📊 查询到新增资产数量: {}", assets.size());
            
            List<Map<String, Object>> assetMaps = new ArrayList<>();
            for (TrafficAsset asset : assets) {
                assetMaps.add(convertToMap(asset));
            }
            
            result.put("success", true);
            result.put("data", assetMaps);
            result.put("count", assetMaps.size());
            result.put("message", "新增资产导出成功");
            
            log.info("✅ 新增资产导出完成: 共{}条记录", assetMaps.size());
            
        } catch (Exception e) {
            log.error("❌ 导出新增资产失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> exportAlertRecords(LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导出报警记录: {} 到 {}", startTime, endTime);
            
            QueryWrapper<AlertRecord> wrapper = new QueryWrapper<>();
            wrapper.between("created_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            log.info("🔍 查询条件: created_time BETWEEN '{}' AND '{}'",
                TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            List<AlertRecord> records = alertRecordMapper.selectList(wrapper);
            log.info("📊 查询到报警记录数量: {}", records.size());
            
            List<Map<String, Object>> recordMaps = new ArrayList<>();
            for (AlertRecord record : records) {
                recordMaps.add(convertToMap(record));
            }
            
            result.put("success", true);
            result.put("data", recordMaps);
            result.put("count", recordMaps.size());
            result.put("message", "报警记录导出成功");
            
            log.info("✅ 报警记录导出完成: 共{}条记录", recordMaps.size());
            
        } catch (Exception e) {
            log.error("❌ 导出报警记录失败", e);
            result.put("success", false);
            result.put("message", "导出失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> packImages(List<String> imagePaths, String outputPath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始打包图片文件: 共{}个文件", imagePaths.size());
            
            // 创建输出目录
            Path outputDir = Paths.get(outputPath).getParent();
            if (outputDir != null && !Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }
            
            int successCount = 0;
            int failedCount = 0;
            long totalSize = 0;
            
            try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(outputPath))) {
                for (String imagePath : imagePaths) {
                    try {
                        File imageFile = new File(imagePath);
                        if (!imageFile.exists()) {
                            log.warn("⚠️ 图片文件不存在: {}", imagePath);
                            failedCount++;
                            continue;
                        }
                        
                        // 使用相对路径作为ZIP内的路径
                        String entryName = imageFile.getName();
                        ZipEntry entry = new ZipEntry(entryName);
                        zos.putNextEntry(entry);
                        
                        try (FileInputStream fis = new FileInputStream(imageFile)) {
                            byte[] buffer = new byte[8192];
                            int length;
                            while ((length = fis.read(buffer)) > 0) {
                                zos.write(buffer, 0, length);
                            }
                        }
                        
                        zos.closeEntry();
                        totalSize += imageFile.length();
                        successCount++;
                        
                    } catch (Exception e) {
                        log.error("❌ 打包图片失败: {}", imagePath, e);
                        failedCount++;
                    }
                }
            }
            
            result.put("success", true);
            result.put("outputPath", outputPath);
            result.put("totalFiles", imagePaths.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("totalSize", totalSize);
            result.put("message", "图片打包完成");
            
            log.info("✅ 图片打包完成: 成功{}个, 失败{}个, 总大小{}KB", 
                successCount, failedCount, totalSize / 1024);
            
        } catch (Exception e) {
            log.error("❌ 打包图片失败", e);
            result.put("success", false);
            result.put("message", "打包失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> generateExportPackage(LocalDateTime startTime, LocalDateTime endTime, 
                                                    boolean includeImages, String outputDir) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始生成导出包: {} 到 {}, 包含图片: {}", startTime, endTime, includeImages);
            
            // 创建输出目录
            Path outputPath = Paths.get(outputDir);
            if (!Files.exists(outputPath)) {
                Files.createDirectories(outputPath);
            }
            
            // 生成文件名
            String timestamp = TimeUtils.nowFilename();
            String dataFileName = "inspection_data_" + timestamp + ".json";
            String imageFileName = "inspection_images_" + timestamp + ".zip";
            
            // 导出各类数据
            Map<String, Object> packageData = new HashMap<>();
            packageData.put("exportTime", TimeUtils.formatStandard(TimeUtils.now()));
            packageData.put("dataStartTime", TimeUtils.formatStandard(startTime));
            packageData.put("dataEndTime", TimeUtils.formatStandard(endTime));
            
            // 导出检测记录
            Map<String, Object> detectionResult = exportDetectionRecords(startTime, endTime);
            if ((Boolean) detectionResult.get("success")) {
                packageData.put("detectionRecords", detectionResult.get("data"));
                log.info("✅ 检测记录导出成功: {}条", detectionResult.get("count"));
            } else {
                packageData.put("detectionRecords", new ArrayList<>());
                log.warn("⚠️ 检测记录导出失败: {}", detectionResult.get("message"));
            }

            // 导出巡检记录
            Map<String, Object> inspectionResult = exportInspectionRecords(startTime, endTime);
            if ((Boolean) inspectionResult.get("success")) {
                packageData.put("inspectionRecords", inspectionResult.get("data"));
                log.info("✅ 巡检记录导出成功: {}条", inspectionResult.get("count"));
            } else {
                packageData.put("inspectionRecords", new ArrayList<>());
                log.warn("⚠️ 巡检记录导出失败: {}", inspectionResult.get("message"));
            }

            // 导出新增资产
            Map<String, Object> assetResult = exportNewAssets(startTime, endTime);
            if ((Boolean) assetResult.get("success")) {
                packageData.put("newAssets", assetResult.get("data"));
                log.info("✅ 新增资产导出成功: {}条", assetResult.get("count"));
            } else {
                packageData.put("newAssets", new ArrayList<>());
                log.warn("⚠️ 新增资产导出失败: {}", assetResult.get("message"));
            }

            // 导出报警记录
            Map<String, Object> alertResult = exportAlertRecords(startTime, endTime);
            if ((Boolean) alertResult.get("success")) {
                packageData.put("alertRecords", alertResult.get("data"));
                log.info("✅ 报警记录导出成功: {}条", alertResult.get("count"));
            } else {
                packageData.put("alertRecords", new ArrayList<>());
                log.warn("⚠️ 报警记录导出失败: {}", alertResult.get("message"));
            }
            
            // 保存数据文件
            String dataFilePath = Paths.get(outputDir, dataFileName).toString();
            try (FileWriter writer = new FileWriter(dataFilePath)) {
                objectMapper.writeValue(writer, packageData);
            }
            
            // 创建统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("detectionRecordCount",
                (Boolean) detectionResult.get("success") ? (Integer) detectionResult.get("count") : 0);
            statistics.put("inspectionRecordCount",
                (Boolean) inspectionResult.get("success") ? (Integer) inspectionResult.get("count") : 0);
            statistics.put("newAssetCount",
                (Boolean) assetResult.get("success") ? (Integer) assetResult.get("count") : 0);
            statistics.put("alertRecordCount",
                (Boolean) alertResult.get("success") ? (Integer) alertResult.get("count") : 0);

            int totalRecords = statistics.values().stream()
                .mapToInt(count -> (Integer) count)
                .sum();
            statistics.put("totalRecords", totalRecords);

            result.put("success", true);
            result.put("dataFile", dataFilePath);
            result.put("statistics", statistics);
            
            // 处理图片文件
            if (includeImages) {
                // 收集图片路径
                List<String> imagePaths = collectImagePaths(startTime, endTime);
                if (!imagePaths.isEmpty()) {
                    String imageFilePath = Paths.get(outputDir, imageFileName).toString();
                    Map<String, Object> imageResult = packImages(imagePaths, imageFilePath);
                    result.put("imageFile", imageFilePath);
                    result.put("imagePackResult", imageResult);
                }
            }
            
            result.put("message", "导出包生成成功");
            log.info("✅ 导出包生成完成: {}", dataFilePath);
            
        } catch (Exception e) {
            log.error("❌ 生成导出包失败", e);
            result.put("success", false);
            result.put("message", "生成失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 收集指定时间范围内的图片路径
     */
    private List<String> collectImagePaths(LocalDateTime startTime, LocalDateTime endTime) {
        List<String> imagePaths = new ArrayList<>();
        
        try {
            // 从检测记录中收集图片
            QueryWrapper<DetectionRecord> wrapper = new QueryWrapper<>();
            wrapper.between("created_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            wrapper.isNotNull("image_url");
            List<DetectionRecord> records = detectionRecordMapper.selectList(wrapper);
            
            for (DetectionRecord record : records) {
                if (record.getImageUrl() != null && !record.getImageUrl().isEmpty()) {
                    // 将URL转换为实际文件路径
                    String imagePath = convertUrlToFilePath(record.getImageUrl());
                    if (imagePath != null) {
                        imagePaths.add(imagePath);
                    }
                }
            }
            
            // 从资产记录中收集图片
            QueryWrapper<TrafficAsset> assetWrapper = new QueryWrapper<>();
            assetWrapper.between("created_time", TimeUtils.formatStandard(startTime), TimeUtils.formatStandard(endTime));
            assetWrapper.isNotNull("detection_image_url");
            List<TrafficAsset> assets = trafficAssetMapper.selectList(assetWrapper);
            
            for (TrafficAsset asset : assets) {
                if (asset.getDetectionImageUrl() != null && !asset.getDetectionImageUrl().isEmpty()) {
                    String imagePath = convertUrlToFilePath(asset.getDetectionImageUrl());
                    if (imagePath != null) {
                        imagePaths.add(imagePath);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("❌ 收集图片路径失败", e);
        }
        
        return imagePaths;
    }
    
    /**
     * 将URL转换为文件路径
     */
    private String convertUrlToFilePath(String imageUrl) {
        try {
            // URL格式: /assets/2024-01-15/filename.jpg
            // 转换为: D:\资产管理图片文件\2024-01-15\filename.jpg
            if (imageUrl.startsWith("/assets/")) {
                String relativePath = imageUrl.substring("/assets/".length());
                return "D:\\资产管理图片文件\\" + relativePath.replace("/", "\\");
            }
        } catch (Exception e) {
            log.warn("⚠️ URL转换失败: {}", imageUrl);
        }
        return null;
    }
    
    /**
     * 将实体对象转换为Map
     */
    private Map<String, Object> convertToMap(Object entity) {
        try {
            String json = objectMapper.writeValueAsString(entity);
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            log.error("❌ 对象转换失败", e);
            return new HashMap<>();
        }
    }
}
