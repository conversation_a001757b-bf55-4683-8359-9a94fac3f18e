package com.ybda.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.model.entity.AlertRecord;
import com.ybda.model.entity.DetectionDetail;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.PendingAssetCheck;
import com.ybda.service.AssetIntegrityService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 资产完整性验证服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AssetIntegrityServiceImpl implements AssetIntegrityService {
    
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final int EXISTENCE_TIMEOUT_MINUTES = 1; // 存在性检查超时时间
    private static final int CORRESPONDENCE_TIMEOUT_MINUTES = 1; // 对应性检查超时时间
    private static final double MATCHING_DISTANCE = 50.0; // 匹配距离（米）
    private static final int RECENT_CHECK_SECONDS = 10; // 10秒内认为是近期检查
    private static final int NEW_ASSET_THRESHOLD_SECONDS = 7; // 超过7秒认为是新资产
    
    @Override
    public void createExistenceCheck(String deviceId, String expectedAssetId, String assetType,
                                     String assetName, Double latitude, Double longitude, String deviceLocationId) {
        try {
            // 检查是否已存在近期的检查记录（避免重复创建）
            if (hasRecentCheck(deviceId, expectedAssetId)) {
                log.debug("🔍 资产已有近期检查记录，跳过创建: deviceId={}, assetId={}", deviceId, expectedAssetId);
                return;
            }

            PendingAssetCheck check = new PendingAssetCheck();
            check.setDeviceId(deviceId);
            check.setGpsTrackId(deviceLocationId);
            check.setExpectedAssetId(expectedAssetId);
            check.setAssetType(assetType);
            check.setAssetName(assetName);
            // 设置预期资产位置（来自资产数据库）
            check.setExpectedLatitude(latitude != null ? latitude : 0.0);
            check.setExpectedLongitude(longitude != null ? longitude : 0.0);
            // 检测位置初始为null，等待后续检测确认
            check.setDetectedLatitude(null);
            check.setDetectedLongitude(null);
            check.setPassedTime(LocalDateTime.now());
            check.setCheckType("存在性");
            check.setStatus("等待");
            check.setCreatedTime(LocalDateTime.now());

            pendingAssetCheckMapper.insert(check);

            log.info("🔍 创建存在性检查任务: deviceId={}, assetId={}, assetName='{}'",
                deviceId, expectedAssetId, assetName);

        } catch (Exception e) {
            log.error("❌ 创建存在性检查任务失败: assetId={}", expectedAssetId, e);
        }
    }
    
    @Override
    public void processDetectionResult(DetectionRecord detectionRecord) {
        try {
            // 1. 处理存在性检查确认
            confirmExistenceChecks(detectionRecord);
            
            // 2. 处理对应性检查更新
//            updateCorrespondenceChecks(detectionRecord);
            
        } catch (Exception e) {
            log.error("❌ 处理检测结果失败: detectionId={}", detectionRecord.getId(), e);
        }
    }
    
    /**
     * 确认存在性检查
     */
    private void confirmExistenceChecks(DetectionRecord detectionRecord) {
        try {
            if (!detectionRecord.hasValidGpsLocation()) {
                return;
            }
            
            // 查找附近的待确认存在性检查
            List<PendingAssetCheck> nearbyChecks = pendingAssetCheckMapper.findNearbyPendingChecks(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                "存在性",
                "等待"
            );
            
            // 获取检测详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());
            
            for (PendingAssetCheck check : nearbyChecks) {
                // 检查是否有匹配的检测详情
                boolean found = detectionDetails.stream()
                    .anyMatch(detail -> 
                        check.getAssetType().equals(detail.getType()) && 
                        (check.getAssetName().equals(detail.getName()) || 
                         isAssetNameSimilar(check.getAssetName(), detail.getName()))
                    );
                
                if (found) {
                    // 确认检查
                    check.setStatus("已确认");
                    check.setConfirmedDetectionId(detectionRecord.getId());
                    check.setConfirmedTime(LocalDateTime.now());
                    pendingAssetCheckMapper.updateById(check);
                    
                    log.info("✅ 存在性检查确认: checkId={}, assetName='{}'", 
                        check.getId(), check.getAssetName());
                }
            }
            
        } catch (Exception e) {
            log.error("❌ 确认存在性检查失败: detectionId={}", detectionRecord.getId(), e);
        }
    }

    @Override
    public void checkTimeoutTasks() {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 检查存在性检查超时
            LocalDateTime existenceTimeout = now.minusMinutes(EXISTENCE_TIMEOUT_MINUTES);
            List<PendingAssetCheck> timeoutExistenceChecks = pendingAssetCheckMapper
                .findTimeoutChecks("存在性", "等待", existenceTimeout);

            for (PendingAssetCheck check : timeoutExistenceChecks) {
                check.setStatus("缺失");
                check.setMissingReason("超时");
                pendingAssetCheckMapper.updateById(check);
                
                // 触发缺失报警
                triggerMissingAssetAlert(check);
                
                log.warn("⏰ 存在性检查超时: checkId={}, assetName='{}'", 
                    check.getId(), check.getAssetName());
            }
            
            // 检查对应性检查超时
            LocalDateTime correspondenceTimeout = now.minusMinutes(CORRESPONDENCE_TIMEOUT_MINUTES);
            List<PendingAssetCheck> timeoutCorrespondenceChecks = pendingAssetCheckMapper
                .findTimeoutChecks("对应性", "等待", correspondenceTimeout);

            for (PendingAssetCheck check : timeoutCorrespondenceChecks) {
                // 这里简化处理：直接标记为超时
                check.setStatus("超时");
                check.setConfirmedTime(now);
                check.setMissingReason("对应性检查超时");
                pendingAssetCheckMapper.updateById(check);

                log.info("⏰ 对应性检查超时处理: checkId={}, 状态=超时",
                    check.getId());
            }
            
        } catch (Exception e) {
            log.error("❌ 检查超时任务失败", e);
        }
    }
    
    /**
     * 触发缺失资产报警
     */
    private void triggerMissingAssetAlert(PendingAssetCheck check) {
        try {
            AlertRecord alert = new AlertRecord();
            alert.setAlertType("ASSET_MISSING");
            alert.setAssetId(check.getExpectedAssetId());
            alert.setAlertMessage(String.format("资产缺失：期望在位置检测到'%s'，但未发现", check.getAssetName()));
            
            Map<String, Object> details = new HashMap<>();
            details.put("checkId", check.getId());
            details.put("deviceId", check.getDeviceId());
            details.put("assetType", check.getAssetType());
            details.put("assetName", check.getAssetName());
            details.put("expectedLocation", Map.of("lat", check.getGpsLatitude(), "lng", check.getGpsLongitude()));
            details.put("passedTime", check.getPassedTime());
            details.put("missingReason", check.getMissingReason());
            
            alert.setAlertData(objectMapper.writeValueAsString(details));
            alert.setStatus("ACTIVE");
            alert.setCreatedTime(LocalDateTime.now());
            
            alertRecordMapper.insert(alert);
            
            log.warn("🚨 触发缺失资产报警: assetName='{}', location=({}, {})", 
                check.getAssetName(), check.getGpsLatitude(), check.getGpsLongitude());
            
        } catch (Exception e) {
            log.error("❌ 触发缺失资产报警失败: checkId={}", check.getId(), e);
        }
    }

    /**
     * 检查资产名称是否相似
     */
    private boolean isAssetNameSimilar(String expected, String actual) {
        if (expected == null || actual == null) {
            return false;
        }
        
        // 简单的相似性检查，可以根据需要扩展
        return expected.contains(actual) || actual.contains(expected) ||
               expected.toLowerCase().contains(actual.toLowerCase()) ||
               actual.toLowerCase().contains(expected.toLowerCase());
    }

    /**
     * 检查是否已存在近期的检查记录
     * 避免在短时间内为同一个资产重复创建检查记录
     */
    private boolean hasRecentCheck(String deviceId, String expectedAssetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusSeconds(RECENT_CHECK_SECONDS);

            // 查询近期是否已有该资产的检查记录
            List<PendingAssetCheck> recentChecks = pendingAssetCheckMapper.selectList(
                new QueryWrapper<PendingAssetCheck>()
                    .eq("device_id", deviceId)
                    .eq("expected_asset_id", expectedAssetId)
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            if (recentChecks.isEmpty()) {
                return false; // 没有近期记录
            }

            PendingAssetCheck recentCheck = recentChecks.get(0);

            // 计算时间差（秒）
            long secondsDiff = java.time.Duration.between(recentCheck.getCreatedTime(), LocalDateTime.now()).getSeconds();

            if (secondsDiff > NEW_ASSET_THRESHOLD_SECONDS) {
                // 超过7秒，认为是新资产，不合并
                log.debug("🆕 超过{}秒阈值，认为是新资产: deviceId={}, assetId={}, 时间差={}秒",
                    NEW_ASSET_THRESHOLD_SECONDS, deviceId, expectedAssetId, secondsDiff);
                return false;
            } else {
                // 7秒内，认为是同一资产的重复检测，合并
                log.debug("🔄 {}秒内重复检测，合并处理: deviceId={}, assetId={}, 时间差={}秒",
                    NEW_ASSET_THRESHOLD_SECONDS, deviceId, expectedAssetId, secondsDiff);
                return true;
            }

        } catch (Exception e) {
            log.warn("⚠️ 检查近期记录失败: deviceId={}, assetId={}", deviceId, expectedAssetId, e);
            return false; // 出错时允许创建，避免影响正常流程
        }
    }
}
