package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.dto.AlertDetailData;
import com.ybda.model.entity.AlertRecord;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.utils.SignParsingUtils;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标牌地面标线匹配服务实现类（重构版）
 * 移除了 SignVerificationTask，直接使用 InspectionRecord
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SignGroundMatchingServiceImpl implements SignGroundMatchingService {

    private final InspectionRecordMapper inspectionRecordMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final DetectionRecordMapper detectionRecordMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final ObjectMapper objectMapper;

    // 配置参数
    private static final double MATCHING_DISTANCE = 30.0; // 30米范围内认为是同一路口（提高精度）
    private static final double SIGN_INTERFERENCE_DISTANCE = 200.0; // 地面标线与空中标牌的最大对应距离
    private static final int VERIFICATION_TIMEOUT_MINUTES = 3; // 3分钟验证超时
    private static final double SUCCESS_THRESHOLD_RATE = 0.8; // 成功阈值：80%

    @Override
    public void processOverheadSignDetection(DetectionRecord detectionRecord, TrafficAsset sign) {
        try {
            log.info("🏷️ 处理空中标牌检测: signName='{}', recordId={}", sign.getName(), detectionRecord.getId());

            // 解析标牌车道信息
            List<SignParsingUtils.LaneInfo> laneInfos = SignParsingUtils.parseOverheadSign(sign.getName());

            if (laneInfos.isEmpty()) {
                log.debug("ℹ️ 标牌无车道指示信息，跳过地面验证: signName='{}'", sign.getName());
                return;
            }

            // 🚨 重要：检测到新的空中标牌时，先结束所有进行中的对应性检测（不管距离）
            // 因为理论上不会同时存在两个空中标牌和地面标线的对应性检测
            List<InspectionRecord> allActiveInspections = inspectionRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<InspectionRecord>()
                    .eq(InspectionRecord::getInspectionType, "对应性")
                    .eq(InspectionRecord::getIsCompleted, 0)
            );

            if (!allActiveInspections.isEmpty()) {
                log.warn("🛑 检测到新空中标牌，强制结束{}个进行中的对应性检测", allActiveInspections.size());
                finishNearbyInspectionRecords(allActiveInspections, "NEW_SIGN_ARRIVAL");
            }

            // 查找是否已存在相同位置的进行中巡检（30米范围内，相同标牌名称）
            List<InspectionRecord> existingInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                sign.getName()
            );

            InspectionRecord inspection;
            if (!existingInspections.isEmpty()) {
                // 存在进行中的巡检，复用现有巡检
                inspection = existingInspections.get(0);
                log.info("🔄 复用现有巡检记录: inspectionId={}", inspection.getId());

            } else {
                // 创建新的巡检记录
                Integer nextNumber = inspectionRecordMapper.getNextInspectionNumberBySignAndLocation(
                        sign.getName(),
                    detectionRecord.getGpsLatitude(), 
                    detectionRecord.getGpsLongitude(), 
                    MATCHING_DISTANCE
                );
                if (nextNumber == null) {
                    nextNumber = 1;
                }

                inspection = new InspectionRecord();
                inspection.setAssetId(sign.getAssetId());
                inspection.setInspectionType("对应性");
                inspection.setInspectionNumber(nextNumber);
                inspection.setInspectionTime(LocalDateTime.now());
                inspection.setFoundMarkingsJson("[]");
                inspection.setFoundMarkingsCount(0);
                inspection.setExpectedMarkingsCount(laneInfos.size());
                inspection.calculateCompletionRate();
                inspection.setFinished(false);
                inspection.setGpsLatitude(detectionRecord.getGpsLatitude());
                inspection.setGpsLongitude(detectionRecord.getGpsLongitude());
                inspection.setSignName(sign.getName());
                inspection.setExpectedLanesJson(objectMapper.writeValueAsString(laneInfos));
                inspection.setFirstDetectionTime(detectionRecord.getCreatedTime());
                inspection.setVerificationTimeout(LocalDateTime.now().plusMinutes(VERIFICATION_TIMEOUT_MINUTES));
                inspection.setCreatedTime(LocalDateTime.now());

                // 保存到数据库
                inspectionRecordMapper.insert(inspection);

                log.info("⏰ 创建标牌巡检记录: inspectionId={}, 第{}次巡检, 期望车道数={}",
                    inspection.getId(), nextNumber, laneInfos.size());

                // 打印期望的地面标线
                List<String> expectedMarkings = SignParsingUtils.generateExpectedGroundMarkings(laneInfos);
                log.info("📋 期望的地面标线: {}", expectedMarkings);
            }

        } catch (Exception e) {
            log.error("❌ 处理空中标牌检测失败: signName='{}', recordId={}", sign.getName(), detectionRecord.getId(), e);
        }
    }

    @Override
    public void processGroundMarkingDetection(DetectionRecord detectionRecord, String markingName) {
        try {
            log.info("🛣️ 处理地面标线检测: markingName='{}', recordId={}", markingName, detectionRecord.getId());

            // 查找附近的进行中标牌巡检
            List<InspectionRecord> nearbyInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                null // 不限制标牌名称
            );

            if (nearbyInspections.isEmpty()) {
                log.debug("ℹ️ 附近没有进行中的标牌巡检");
                return;
            }

            // 🚦 检查是否是路口结束标识（斑马线或停止线）
            if (isIntersectionEndMarking(markingName)) {
                log.info("🏁 检测到路口结束标识: '{}', 结束附近的标牌巡检", markingName);
                finishNearbyInspectionRecords(nearbyInspections, markingName);
                return;
            }

            // 检查地面标线是否匹配任何标牌巡检
            for (InspectionRecord inspection : nearbyInspections) {
                if (inspection.isFinished()) {
                    continue;
                }

                // 🎯 关键：判断地面标线与空中标牌的距离，必须小于200米才进行对应性检测
                double distanceToSign = calculateDistance(
                    detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude(),
                    inspection.getGpsLatitude(), inspection.getGpsLongitude()
                );

                if (distanceToSign > SIGN_INTERFERENCE_DISTANCE) {
                    log.info("📏 地面标线距离空中标牌{}米，超过200米阈值，跳过对应性检测: marking='{}', signName='{}'",
                        String.format("%.1f", distanceToSign), markingName, inspection.getSignName());
                    continue; // 距离太远，不进行对应性检测，但会正常记录资产
                }

                // 解析期望的车道信息
                List<SignParsingUtils.LaneInfo> expectedLanes = parseExpectedLanes(inspection.getExpectedLanesJson());

                if (SignParsingUtils.isGroundMarkingMatchingSign(expectedLanes, markingName)) {
                    // 检查是否需要创建新的巡检记录（基于检测帧的变化）
                    InspectionRecord currentInspection = getOrCreateInspectionForDetection(inspection, detectionRecord);

                    // 匹配成功，按车道位置更新巡检记录
                    updateInspectionFoundMarkingsWithLanePosition(currentInspection, markingName, detectionRecord);

                    log.info("✅ 地面标线匹配成功: inspectionId={}, marking='{}', 距离={}米, 当前巡检已找到={}/{}, 第{}次巡检",
                        currentInspection.getId(), markingName, String.format("%.1f", distanceToSign),
                        currentInspection.getFoundMarkingsCount(),
                        currentInspection.getExpectedMarkingsCount(), currentInspection.getInspectionNumber());

                    // 🚨 关键：每次更新后立即计算匹配度并判断是否需要报警
                    checkAndTriggerCorrespondenceAlert(currentInspection);

                    break;
                } else {
                    log.debug("🔍 地面标线'{}' 与标牌'{}'不匹配，距离={}米",
                        markingName, inspection.getSignName(), String.format("%.1f", distanceToSign));
                }
            }

        } catch (Exception e) {
            log.error("❌ 处理地面标线检测失败: markingName='{}', recordId={}", markingName, detectionRecord.getId(), e);
        }
    }

    @Override
    public void checkTimeoutSignVerifications() {
        try {
            // 查询所有超时的巡检记录
            List<InspectionRecord> timeoutInspections = inspectionRecordMapper.selectTimeoutInspections();

            for (InspectionRecord inspection : timeoutInspections) {
                // 完成超时的巡检记录
                inspection.setFinished(true);
                inspection.setEndMarkingName("TIMEOUT");
                inspection.calculateCompletionRate();
                inspectionRecordMapper.updateById(inspection);

                log.warn("⏰ 标牌巡检超时: inspectionId={}, signName='{}', 第{}次巡检 (未检测到路口结束标识)",
                    inspection.getId(), inspection.getSignName(), inspection.getInspectionNumber());

                // 🚨 超时报警
                createCorrespondenceAlert(inspection,
                    inspection.getFoundMarkingsCount(),
                    inspection.getExpectedMarkingsCount(),
                    "TIMEOUT");

                // 基于历史巡检记录评估是否需要报警
                evaluateInspectionHistory(inspection);
            }

        } catch (Exception e) {
            log.error("❌ 检查超时验证任务失败", e);
        }
    }

    /**
     * 判断是否是路口结束标识
     */
    private boolean isIntersectionEndMarking(String markingName) {
        if (markingName == null) {
            return false;
        }

        String lowerName = markingName.toLowerCase();

        // 斑马线关键词
        if (lowerName.contains("斑马线") || lowerName.contains("人行横道") ||
            lowerName.contains("crosswalk") || lowerName.contains("zebra")) {
            return true;
        }

        // 停止线关键词
        if (lowerName.contains("停止线") || lowerName.contains("停车线") ||
            lowerName.contains("stop line") || lowerName.contains("stop")) {
            return true;
        }

        return false;
    }

    /**
     * 结束附近的巡检记录
     */
    private void finishNearbyInspectionRecords(List<InspectionRecord> inspections, String endMarkingName) {
        for (InspectionRecord inspection : inspections) {
            if (inspection.isFinished()) {
                continue;
            }

            // 完成巡检记录
            inspection.setFinished(true);
            inspection.setEndMarkingName(endMarkingName);
            inspection.calculateCompletionRate();
            inspectionRecordMapper.updateById(inspection);

            // 判断巡检结果
            int foundCount = inspection.getFoundMarkingsCount();
            int expectedCount = inspection.getExpectedMarkingsCount();
            int successThreshold = Math.max(1, (int) Math.ceil(expectedCount * SUCCESS_THRESHOLD_RATE));

            double actualRate = expectedCount > 0 ? (double) foundCount / expectedCount : 0.0;

            if (foundCount >= successThreshold) {
                log.info("🎉 第{}次巡查验证成功: 匹配度={}/{} ({:.1f}%), 结束标识='{}', 阈值={}%",
                    inspection.getInspectionNumber(), foundCount, expectedCount,
                    actualRate * 100, endMarkingName, (int)(SUCCESS_THRESHOLD_RATE * 100));
            } else {
                log.warn("⚠️ 第{}次巡查验证不完整: 匹配度={}/{} ({:.1f}%), 结束标识='{}', 低于阈值{}%",
                    inspection.getInspectionNumber(), foundCount, expectedCount,
                    actualRate * 100, endMarkingName, (int)(SUCCESS_THRESHOLD_RATE * 100));

                // 🚨 匹配度低于80%，创建报警
                createCorrespondenceAlert(inspection, foundCount, expectedCount, "INCOMPLETE_MATCH");
            }

            // 评估历史记录
            evaluateInspectionHistory(inspection);
        }
    }

    /**
     * 更新巡检记录中找到的标线（简单版本，保持向后兼容）
     */
    private void updateInspectionFoundMarkings(InspectionRecord inspection, String markingName) {
        try {
            log.debug("🔄 开始简单模式更新标线: inspectionId={}, markingName={}",
                inspection.getId(), markingName);

            // 解析已找到的标线
            List<String> foundMarkings = parseFoundMarkings(inspection.getFoundMarkingsJson());
            log.debug("🔍 当前已找到的标线: {}", foundMarkings);

            // 避免重复添加
            if (!foundMarkings.contains(markingName)) {
                foundMarkings.add(markingName);
                log.debug("➕ 添加新标线: {}, 总数: {}", markingName, foundMarkings.size());

                // 更新记录
                String newFoundMarkingsJson = objectMapper.writeValueAsString(foundMarkings);
                inspection.setFoundMarkingsJson(newFoundMarkingsJson);
                inspection.setFoundMarkingsCount(foundMarkings.size());
                inspection.calculateCompletionRate();

                log.debug("💾 准备更新数据库: foundMarkingsJson={}, count={}",
                    newFoundMarkingsJson, foundMarkings.size());

                inspectionRecordMapper.updateById(inspection);
                log.debug("✅ 数据库更新成功");
            } else {
                log.debug("🔄 标线已存在，跳过: {}", markingName);
            }

        } catch (Exception e) {
            log.error("❌ 更新巡检找到的标线失败: inspectionId={}, markingName={}, foundMarkingsJson={}",
                inspection.getId(), markingName, inspection.getFoundMarkingsJson(), e);
        }
    }

    /**
     * 按车道位置更新巡检记录中找到的标线
     * 处理多次检测同一标线的情况
     */
    private void updateInspectionFoundMarkingsWithLanePosition(InspectionRecord inspection, String markingName, DetectionRecord detectionRecord) {
        try {
            // 解析已找到的标线
            List<String> foundMarkings = parseFoundMarkings(inspection.getFoundMarkingsJson());

            // 解析期望的车道信息
            List<SignParsingUtils.LaneInfo> expectedLanes = parseExpectedLanes(inspection.getExpectedLanesJson());

            // 获取当前检测记录中所有地面箭头标线的位置信息
            List<GroundMarkingPosition> currentMarkings = extractGroundMarkingPositions(detectionRecord);

            // 找到所有同名的标线（处理同一检测中的多个相同标线）
            List<GroundMarkingPosition> sameNameMarkings = currentMarkings.stream()
                .filter(gm -> markingName.equals(gm.getName()))
                .collect(Collectors.toList());

            log.info("🔍 找到{}个'{}'标线需要处理", sameNameMarkings.size(), markingName);

            // 处理每个同名标线
            for (GroundMarkingPosition currentMarking : sameNameMarkings) {
                log.info("🎯 处理标线: '{}' (centerX={})", markingName, currentMarking.getCenterX());
                // 智能匹配车道位置（处理部分遮挡情况）
                log.info("🔍 开始车道映射: markingName='{}', currentMarking.centerX={}, 总标线数={}",
                    markingName, currentMarking.getCenterX(), currentMarkings.size());

                int matchedLaneNumber = smartMatchLaneByPosition(currentMarking, currentMarkings, expectedLanes);
                String laneSpecificMarking = String.format("车道%d:%s", matchedLaneNumber, markingName);

                log.info("🎯 车道映射结果: '{}' (centerX={}) → '{}'",
                    markingName, currentMarking.getCenterX(), laneSpecificMarking);

                // 处理多次检测的情况
                boolean shouldAdd = handleMultipleDetections(inspection, laneSpecificMarking, detectionRecord, foundMarkings);

                if (shouldAdd) {
                    foundMarkings.add(laneSpecificMarking);

                    // 更新记录
                    inspection.setFoundMarkingsJson(objectMapper.writeValueAsString(foundMarkings));
                    inspection.setFoundMarkingsCount(foundMarkings.size());
                    inspection.calculateCompletionRate();
                    inspectionRecordMapper.updateById(inspection);

                    log.info("🎯 按车道位置添加标线: 车道{} → {} (位置X={}, 检测到{}/{}个车道, 帧{})",
                        matchedLaneNumber, markingName, currentMarking.getCenterX(),
                        currentMarkings.size(), expectedLanes.size(), detectionRecord.getId());
                } else {
                    log.debug("🔄 重复检测跳过: 车道{} → {} (帧{})",
                        matchedLaneNumber, markingName, detectionRecord.getId());
                }
            }

            // 如果没有找到任何同名标线，回退到简单模式
            if (sameNameMarkings.isEmpty()) {
                log.warn("⚠️ 未找到标线'{}'的位置信息，回退到简单模式", markingName);
                updateInspectionFoundMarkings(inspection, markingName);
            }

        } catch (Exception e) {
            log.error("❌ 按车道位置更新巡检找到的标线失败: inspectionId={}, markingName={}",
                inspection.getId(), markingName, e);
            log.error("❌ 异常详情: expectedLanesJson={}, foundMarkingsJson={}",
                inspection.getExpectedLanesJson(), inspection.getFoundMarkingsJson());

            // 出错时回退到简单模式
            try {
                log.info("🔄 回退到简单模式更新标线: inspectionId={}, markingName={}",
                    inspection.getId(), markingName);
                updateInspectionFoundMarkings(inspection, markingName);
                log.info("✅ 简单模式更新成功");
            } catch (Exception fallbackException) {
                log.error("❌ 简单模式更新也失败了: inspectionId={}, markingName={}",
                    inspection.getId(), markingName, fallbackException);
            }
        }
    }

    /**
     * 解析期望的车道信息
     */
    private List<SignParsingUtils.LaneInfo> parseExpectedLanes(String expectedLanesJson) {
        try {
            if (expectedLanesJson == null || expectedLanesJson.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(expectedLanesJson, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.warn("⚠️ 解析期望车道信息失败: {}", expectedLanesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析已找到的标线
     */
    private List<String> parseFoundMarkings(String foundMarkingsJson) {
        try {
            if (foundMarkingsJson == null || foundMarkingsJson.trim().isEmpty() || "[]".equals(foundMarkingsJson.trim())) {
                return new ArrayList<>();
            }
            return objectMapper.readValue(foundMarkingsJson, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.warn("⚠️ 解析已找到标线失败: {}", foundMarkingsJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查并触发对应性检测报警（实时检查）
     * 每次更新InspectionRecord后立即调用
     */
    private void checkAndTriggerCorrespondenceAlert(InspectionRecord inspection) {
        try {
            int foundCount = inspection.getFoundMarkingsCount();
            int expectedCount = inspection.getExpectedMarkingsCount();

            if (expectedCount == 0) {
                return; // 没有期望标线，无需检查
            }

            double completionRate = (double) foundCount / expectedCount;

            // 如果匹配度低于80%，立即触发报警
            if (completionRate < SUCCESS_THRESHOLD_RATE) {
                log.warn("⚠️ 实时检测到匹配度不足: inspectionId={}, 匹配度={}/{} ({:.1f}%), 第{}次巡检",
                    inspection.getId(), foundCount, expectedCount, completionRate * 100, inspection.getInspectionNumber());

                createCorrespondenceAlert(inspection, foundCount, expectedCount, "REALTIME_INCOMPLETE_MATCH");
            } else {
                log.debug("✅ 当前匹配度满足要求: inspectionId={}, 匹配度={}/{} ({:.1f}%)",
                    inspection.getId(), foundCount, expectedCount, completionRate * 100);
            }

        } catch (Exception e) {
            log.error("❌ 检查对应性报警失败: inspectionId={}", inspection.getId(), e);
        }
    }

    /**
     * 创建对应性检测报警
     */
    private void createCorrespondenceAlert(InspectionRecord inspection, int foundCount, int expectedCount, String alertReason) {
        try {
            String alertType = "CORRESPONDENCE_MISMATCH";
            String alertLevel = determineAlertLevel(foundCount, expectedCount, alertReason);

            String alertMessage = String.format(
                "标牌对应性检测异常: %s '%s' 第%d次巡检, 匹配度=%d/%d (%.1f%%), 原因=%s",
                inspection.getSignName(),
                inspection.getSignName(),
                inspection.getInspectionNumber(),
                foundCount,
                expectedCount,
                expectedCount > 0 ? (foundCount * 100.0 / expectedCount) : 0.0,
                getAlertReasonText(alertReason)
            );

            // 🖼️ 关键：获取空中标牌图片和地面标线图片URL，支持图片替换对比
            String signImageUrl = getBestSignImageUrl(inspection);
            String groundImageUrl = getBestGroundImageUrl(inspection);

            // 构建类型化的报警详细数据
            AlertDetailData alertDetailData = new AlertDetailData();

            // 通用信息
            alertDetailData.setAlertReason(alertReason);
            alertDetailData.setAlertReasonText(getAlertReasonText(alertReason));
            alertDetailData.setGpsLatitude(inspection.getGpsLatitude());
            alertDetailData.setGpsLongitude(inspection.getGpsLongitude());
            alertDetailData.setAlertTime(LocalDateTime.now());

            // 对应性检测专用信息
            alertDetailData.setInspectionId(inspection.getId());
            alertDetailData.setSignName(inspection.getSignName());
            alertDetailData.setInspectionNumber(inspection.getInspectionNumber());
            alertDetailData.setFoundCount(foundCount);
            alertDetailData.setExpectedCount(expectedCount);
            alertDetailData.setCompletionRate(inspection.getCompletionRate());
            alertDetailData.setExpectedLanes(inspection.getExpectedLanesJson());
            alertDetailData.setFoundMarkings(inspection.getFoundMarkingsJson());
            alertDetailData.setSignImageUrl(signImageUrl);
            alertDetailData.setGroundImageUrl(groundImageUrl);
            alertDetailData.setFirstDetectionTime(inspection.getFirstDetectionTime());
            alertDetailData.setVerificationTimeout(inspection.getVerificationTimeout());

            log.error("🚨 对应性检测报警: {}", alertMessage);
            log.error("   📍 位置: ({}, {})", inspection.getGpsLatitude(), inspection.getGpsLongitude());
            log.error("   📋 期望标线: {}", inspection.getExpectedLanesJson());
            log.error("   ✅ 找到标线: {}", inspection.getFoundMarkingsJson());
            log.error("   🖼️ 标牌图片: {}", signImageUrl);
            log.error("   🖼️ 地面图片: {}", groundImageUrl);

            // 保存到alert_records表
            AlertRecord alert = new AlertRecord();
            alert.setAlertType(alertType);
            alert.setAssetId(inspection.getAssetId());
            alert.setAlertLevel(alertLevel);
            alert.setAlertMessage(alertMessage);
            alert.setAlertDetailData(alertDetailData);  // 🎯 使用类型化数据
            alert.setSignImageUrl(signImageUrl);  // 🖼️ 空中标牌图片
            alert.setGroundImageUrl(groundImageUrl);  // 🖼️ 地面标线图片
            alert.setStatus("ACTIVE");
            alert.setCreatedTime(LocalDateTime.now());
            alertRecordMapper.insert(alert);

            log.warn("🚨 已创建对应性检测报警记录: alertId={}, level={}", alert.getId(), alertLevel);

        } catch (Exception e) {
            log.error("❌ 创建对应性检测报警失败: inspectionId={}", inspection.getId(), e);
        }
    }

    /**
     * 确定报警级别（基于80%阈值）
     */
    private String determineAlertLevel(int foundCount, int expectedCount, String alertReason) {
        if ("TIMEOUT".equals(alertReason)) {
            return "HIGH"; // 超时是高级别报警
        }

        if ("NEW_SIGN_ARRIVAL".equals(alertReason)) {
            return "MEDIUM"; // 新标牌到来中断是中级别报警
        }

        if ("REALTIME_INCOMPLETE_MATCH".equals(alertReason)) {
            // 实时检测的报警级别基于匹配度动态判断
            if (expectedCount == 0) {
                return "MEDIUM";
            }
            double completionRate = (double) foundCount / expectedCount;
            if (completionRate == 0.0) {
                return "CRITICAL";
            } else if (completionRate < 0.3) {
                return "HIGH";
            } else if (completionRate < 0.6) {
                return "MEDIUM";
            } else {
                return "LOW";
            }
        }

        if (expectedCount == 0) {
            return "MEDIUM";
        }

        double completionRate = (double) foundCount / expectedCount;

        if (completionRate == 0.0) {
            return "CRITICAL"; // 完全没匹配到
        } else if (completionRate < 0.3) {
            return "HIGH"; // 匹配度很低 (<30%)
        } else if (completionRate < 0.6) {
            return "MEDIUM"; // 匹配度较低 (30%-60%)
        } else if (completionRate < SUCCESS_THRESHOLD_RATE) {
            return "LOW"; // 匹配度不足80%但还可以 (60%-80%)
        } else {
            return "LOW"; // 理论上不会到这里，因为>=80%不会报警
        }
    }

    /**
     * 获取报警原因文本
     */
    private String getAlertReasonText(String alertReason) {
        switch (alertReason) {
            case "TIMEOUT":
                return "检测超时";
            case "INCOMPLETE_MATCH":
                return "匹配度不足";
            case "NEW_SIGN_ARRIVAL":
                return "新标牌到来致检测结束";
            case "REALTIME_INCOMPLETE_MATCH":
                return "实时检测匹配度不足";
            default:
                return alertReason;
        }
    }

    /**
     * 评估巡检历史记录
     */
    private void evaluateInspectionHistory(InspectionRecord inspection) {
        try {
            // 查询该标牌位置的最近3次巡检记录
            List<InspectionRecord> recentInspections = inspectionRecordMapper.findNearbyActiveSignInspections(
                inspection.getGpsLatitude(),
                inspection.getGpsLongitude(),
                MATCHING_DISTANCE,
                inspection.getSignName()
            );

            // 这里可以添加基于历史记录的报警逻辑
            // 比如：连续3次匹配度都很低，则触发报警

        } catch (Exception e) {
            log.error("❌ 评估巡检历史记录失败: inspectionId={}", inspection.getId(), e);
        }
    }

    /**
     * 从检测记录中提取地面箭头标线的位置信息
     * 只提取车道方向箭头，过滤掉车道线、停止线等其他标线
     */
    private List<GroundMarkingPosition> extractGroundMarkingPositions(DetectionRecord detectionRecord) {
        List<GroundMarkingPosition> positions = new ArrayList<>();

        try {
            JsonNode rawData = objectMapper.readTree(detectionRecord.getRawData());
            JsonNode signs = rawData.get("signs");

            if (signs != null && signs.isArray()) {
                for (JsonNode sign : signs) {
                    if ("ground_marking".equals(sign.get("type").asText())) {
                        String name = sign.get("name").asText();

                        // 只提取箭头相关的地面标线
                        if (isArrowGroundMarking(name)) {
                            JsonNode bbox = sign.get("bbox");

                            if (bbox != null && bbox.isArray() && bbox.size() >= 4) {
                                double x1 = bbox.get(0).asDouble();
                                double y1 = bbox.get(1).asDouble();
                                double x2 = bbox.get(2).asDouble();
                                double y2 = bbox.get(3).asDouble();

                                // 计算中心点X坐标（用于车道排序）
                                double centerX = (x1 + x2) / 2.0;

                                positions.add(new GroundMarkingPosition(name, centerX, y1, x1, y1, x2, y2));
                                log.debug("🎯 提取箭头标线: {} (位置X={})", name, centerX);
                            }
                        }
                    }
                }
            }

            // 按X坐标排序（从左到右对应车道1到N）
            positions.sort(Comparator.comparingDouble(GroundMarkingPosition::getCenterX));

            log.debug("🎯 共提取到{}个箭头标线", positions.size());

        } catch (Exception e) {
            log.warn("⚠️ 解析地面箭头标线位置信息失败: recordId={}", detectionRecord.getId(), e);
        }

        return positions;
    }

    /**
     * 判断是否为箭头类型的地面标线
     */
    private boolean isArrowGroundMarking(String markingName) {
        if (markingName == null) {
            return false;
        }

        String name = markingName.toLowerCase().trim();

        // 箭头相关的关键词
        return name.contains("直行") ||
               name.contains("左转") ||
               name.contains("右转") ||
               name.contains("掉头") ||
               name.contains("箭头") ||
               "直".equals(name) ||
               "左".equals(name) ||
               "右".equals(name) ||
               "掉".equals(name);
    }

    /**
     * 智能匹配车道位置（处理部分遮挡情况）
     */
    private int smartMatchLaneByPosition(GroundMarkingPosition targetMarking, List<GroundMarkingPosition> allMarkings, List<SignParsingUtils.LaneInfo> expectedLanes) {
        if (allMarkings.isEmpty() || expectedLanes.isEmpty()) {
            return 1;
        }

        int detectedCount = allMarkings.size();
        int expectedCount = expectedLanes.size();

        // 按X坐标排序所有标线（从左到右）
        allMarkings.sort(Comparator.comparingDouble(GroundMarkingPosition::getCenterX));

        log.debug("🎯 车道映射分析: 检测{}个标线, 期望{}个车道", detectedCount, expectedCount);
        for (int i = 0; i < allMarkings.size(); i++) {
            GroundMarkingPosition marking = allMarkings.get(i);
            log.debug("   标线{}: {} (centerX={})", i, marking.getName(), marking.getCenterX());
        }

        // 找到目标标线在排序后列表中的位置
        int positionInDetected = -1;
        for (int i = 0; i < allMarkings.size(); i++) {
            if (Math.abs(allMarkings.get(i).getCenterX() - targetMarking.getCenterX()) < 5) {
                positionInDetected = i;
                log.debug("🎯 找到目标标线位置: {} (centerX={}) → 排序位置{}",
                    targetMarking.getName(), targetMarking.getCenterX(), i);
                break;
            }
        }

        if (positionInDetected == -1) {
            return 1; // 未找到，默认车道1
        }

        // 智能映射逻辑
        int mappedLaneNumber;

        if (detectedCount == expectedCount) {
            // 完全匹配：检测到的数量等于期望数量
            mappedLaneNumber = positionInDetected + 1;
            log.debug("🎯 完全匹配: 位置{} → 车道{}", positionInDetected, mappedLaneNumber);

        } else if (detectedCount < expectedCount) {
            // 部分遮挡：检测到的数量少于期望数量
            mappedLaneNumber = calculateLaneWithOcclusion(positionInDetected, detectedCount, expectedCount, targetMarking, allMarkings);
            log.debug("🎯 部分遮挡: 检测{}/{}个车道, 位置{} → 车道{}",
                detectedCount, expectedCount, positionInDetected, mappedLaneNumber);

        } else {
            // 检测过多：可能有误检，使用简单映射
            mappedLaneNumber = Math.min(positionInDetected + 1, expectedCount);
            log.debug("🎯 检测过多: 位置{} → 车道{} (限制在{}以内)",
                positionInDetected, mappedLaneNumber, expectedCount);
        }

        // 确保车道号在合理范围内
        return Math.max(1, Math.min(mappedLaneNumber, expectedCount));
    }

    /**
     * 计算有遮挡情况下的车道号
     */
    private int calculateLaneWithOcclusion(int positionInDetected, int detectedCount, int expectedCount,
                                         GroundMarkingPosition targetMarking, List<GroundMarkingPosition> allMarkings) {

        // 策略1: 基于标线类型的智能推断
        String markingType = targetMarking.getName();
        int mappedLane;

        // 对于相同类型的标线，使用连续映射而不是比例映射
        if (markingType.contains("直行")) {
            // 直行标线从车道1开始连续映射
            mappedLane = positionInDetected + 1;
            log.debug("🎯 直行标线连续映射: 位置{} → 车道{}", positionInDetected, mappedLane);

        } else {
            // 其他类型使用比例映射
            double relativePosition = (double) positionInDetected / (detectedCount - 1); // 0.0 到 1.0
            mappedLane = (int) Math.round(relativePosition * (expectedCount - 1)) + 1;
            log.debug("🎯 比例映射: 位置{} → 车道{} (相对位置={})",
                positionInDetected, mappedLane, relativePosition);
        }

        // 对于非直行标线，进行特殊位置调整
        if (!markingType.contains("直行")) {
            // 如果是右转箭头，通常在最右侧车道
            if (markingType.contains("右转") || markingType.contains("右")) {
                if (positionInDetected == detectedCount - 1) { // 是最右侧检测到的标线
                    mappedLane = expectedCount; // 映射到最右侧期望车道
                    log.debug("🎯 右转箭头智能映射: 最右侧位置 → 车道{}", mappedLane);
                }
            }

            // 如果是左转箭头，通常在最左侧车道
            else if (markingType.contains("左转") || markingType.contains("左")) {
                if (positionInDetected == 0) { // 是最左侧检测到的标线
                    mappedLane = 1; // 映射到车道1
                    log.debug("🎯 左转箭头智能映射: 最左侧位置 → 车道1");
                }
            }

            // 如果是掉头箭头，通常在最左侧车道
            else if (markingType.contains("掉头") || markingType.contains("掉")) {
                if (positionInDetected == 0) { // 是最左侧检测到的标线
                    mappedLane = 1; // 映射到车道1
                    log.debug("🎯 掉头箭头智能映射: 最左侧位置 → 车道1");
                }
            }
        }

        return mappedLane;
    }

    /**
     * 获取或创建当前检测对应的巡检记录
     * 每次不同位置的检测都创建新的巡检记录
     */
    private InspectionRecord getOrCreateInspectionForDetection(InspectionRecord baseInspection, DetectionRecord detectionRecord) {
        try {
            // 检查是否已经存在当前检测记录对应的巡检记录
            InspectionRecord existingInspection = findExistingInspectionForDetection(baseInspection, detectionRecord);

            if (existingInspection != null) {
                log.debug("🔍 找到现有巡检记录: inspectionId={}, detectionId={}",
                    existingInspection.getId(), detectionRecord.getId());
                return existingInspection;
            }

            // 创建新的巡检记录
            InspectionRecord newInspection = createNewInspectionRecord(baseInspection, detectionRecord);
            log.info("🆕 创建新的巡检记录: inspectionId={}, detectionId={}, 位置=({}, {})",
                newInspection.getId(), detectionRecord.getId(),
                detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());

            return newInspection;

        } catch (Exception e) {
            log.error("❌ 获取或创建巡检记录失败: baseInspectionId={}, detectionId={}",
                baseInspection.getId(), detectionRecord.getId(), e);
            return baseInspection; // 出错时返回原记录
        }
    }

    /**
     * 查找当前检测记录对应的现有巡检记录
     */
    private InspectionRecord findExistingInspectionForDetection(InspectionRecord baseInspection, DetectionRecord detectionRecord) {
        try {
            // 查找相同资产、相同检测记录ID的巡检记录
            List<InspectionRecord> existingInspections = inspectionRecordMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<InspectionRecord>()
                    .eq(InspectionRecord::getAssetId, baseInspection.getAssetId())
                    .eq(InspectionRecord::getInspectionType, "对应性")
                    .eq(InspectionRecord::getDetectionRecordId, detectionRecord.getId())
                    .orderByDesc(InspectionRecord::getCreatedTime)
            );

            return existingInspections.isEmpty() ? null : existingInspections.get(0);

        } catch (Exception e) {
            log.warn("⚠️ 查找现有巡检记录失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 创建新的巡检记录
     */
    private InspectionRecord createNewInspectionRecord(InspectionRecord baseInspection, DetectionRecord detectionRecord) {
        // 计算当前是第几次巡检
        int inspectionNumber = getNextInspectionNumber(baseInspection.getAssetId());

        InspectionRecord newInspection = new InspectionRecord();
        newInspection.setAssetId(baseInspection.getAssetId());
        newInspection.setSignName(baseInspection.getSignName());
        newInspection.setInspectionType("对应性");
        newInspection.setInspectionNumber(inspectionNumber);
        newInspection.setInspectionTime(LocalDateTime.now()); // 🔧 修复：设置巡检时间
        newInspection.setDetectionRecordId(detectionRecord.getId());

        // 位置信息使用检测记录的位置
        newInspection.setGpsLatitude(detectionRecord.getGpsLatitude());
        newInspection.setGpsLongitude(detectionRecord.getGpsLongitude());

        // 复制期望信息
        newInspection.setExpectedLanesJson(baseInspection.getExpectedLanesJson());
        newInspection.setExpectedMarkingsCount(baseInspection.getExpectedMarkingsCount());

        // 初始化找到的标线信息
        newInspection.setFoundMarkingsJson("[]");
        newInspection.setFoundMarkingsCount(0);
        newInspection.setCompletionRate(java.math.BigDecimal.ZERO);

        // 设置超时时间
        newInspection.setVerificationTimeout(LocalDateTime.now().plusMinutes(VERIFICATION_TIMEOUT_MINUTES));
        newInspection.setFinished(false);

        // 保存到数据库
        inspectionRecordMapper.insert(newInspection);

        return newInspection;
    }

    /**
     * 计算两个GPS坐标之间的距离（米）
     * 使用Haversine公式
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        if (lat1 == lat2 && lng1 == lng2) {
            return 0.0;
        }

        final double R = 6371000; // 地球半径（米）
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLngRad = Math.toRadians(lng2 - lng1);

        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 获取空中标牌图片URL
     * 直接使用资产数据库中的图片，不管质量状态
     */
    private String getBestSignImageUrl(InspectionRecord inspection) {
        try {
            // 直接从资产数据库获取空中标牌图片
            String signAssetId = inspection.getAssetId();
            if (signAssetId != null) {
                TrafficAsset asset = trafficAssetMapper.selectById(signAssetId);
                if (asset != null && asset.getDetectionImageUrl() != null) {
                    log.debug("📸 获取空中标牌资产图片: assetId={}, imageUrl={}",
                        signAssetId, asset.getDetectionImageUrl());
                    return asset.getDetectionImageUrl();
                }
            }

            // 如果资产库中没有图片，降级使用检测记录中的图片
            String fallbackUrl = getSignImageUrl(inspection);
            log.debug("⚠️ 资产库中无图片，使用检测记录图片: assetId={}, fallbackUrl={}",
                signAssetId, fallbackUrl);
            return fallbackUrl;

        } catch (Exception e) {
            log.error("❌ 获取空中标牌图片失败: inspectionId={}", inspection.getId(), e);
            return getSignImageUrl(inspection); // 降级处理
        }
    }

    /**
     * 获取地面标线图片URL
     * 直接使用当前检测请求中的图片（实时拍摄的现场图片）
     */
    private String getBestGroundImageUrl(InspectionRecord inspection) {
        try {
            // 地面标线图片直接使用当前检测请求中的图片
            // 这是实时拍摄的现场图片，不需要考虑资产的image_quality_status
            String currentUrl = getGroundImageUrl(inspection);

            log.debug("📸 获取地面标线图片（当前检测图片）: inspectionId={}, imageUrl={}",
                inspection.getId(), currentUrl);

            return currentUrl;

        } catch (Exception e) {
            log.error("❌ 获取地面标线图片失败: inspectionId={}", inspection.getId(), e);
            return getGroundImageUrl(inspection); // 降级到原始方法
        }
    }

    /**
     * 获取空中标牌图片URL（原始方法）
     * 从巡检记录关联的检测记录中获取
     */
    private String getSignImageUrl(InspectionRecord inspection) {
        try {
            if (inspection.getDetectionRecordId() != null) {
                DetectionRecord detectionRecord = detectionRecordMapper.selectById(inspection.getDetectionRecordId());
                if (detectionRecord != null && detectionRecord.getImageUrl() != null) {
                    return detectionRecord.getImageUrl();
                }
            }

            // 如果没有关联的检测记录，尝试通过资产ID和时间范围查找
            if (inspection.getFirstDetectionTime() != null) {
                // 查找首次检测时间附近的检测记录
                List<DetectionRecord> nearbyRecords = detectionRecordMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<DetectionRecord>()
                        .between(DetectionRecord::getCreatedTime,
                            inspection.getFirstDetectionTime().minusMinutes(1),
                            inspection.getFirstDetectionTime().plusMinutes(1))
                        .orderByAsc(DetectionRecord::getCreatedTime)
                );

                if (!nearbyRecords.isEmpty()) {
                    return nearbyRecords.get(0).getImageUrl();
                }
            }

            return null;
        } catch (Exception e) {
            log.error("❌ 获取空中标牌图片URL失败: inspectionId={}", inspection.getId(), e);
            return null;
        }
    }

    /**
     * 获取地面标线图片URL
     * 从最后一次检测记录中获取（导致报警的那次检测）
     */
    private String getGroundImageUrl(InspectionRecord inspection) {
        try {
            if (inspection.getDetectionRecordId() != null) {
                DetectionRecord detectionRecord = detectionRecordMapper.selectById(inspection.getDetectionRecordId());
                if (detectionRecord != null && detectionRecord.getImageUrl() != null) {
                    return detectionRecord.getImageUrl();
                }
            }

            // 如果没有直接关联，查找最近的检测记录
            if (inspection.getInspectionTime() != null) {
                List<DetectionRecord> recentRecords = detectionRecordMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<DetectionRecord>()
                        .between(DetectionRecord::getCreatedTime,
                            inspection.getInspectionTime().minusMinutes(5),
                            inspection.getInspectionTime().plusMinutes(1))
                        .orderByDesc(DetectionRecord::getCreatedTime)
                );

                if (!recentRecords.isEmpty()) {
                    return recentRecords.get(0).getImageUrl();
                }
            }

            return null;
        } catch (Exception e) {
            log.error("❌ 获取地面标线图片URL失败: inspectionId={}", inspection.getId(), e);
            return null;
        }
    }

    /**
     * 获取下一个巡检序号
     */
    private int getNextInspectionNumber(String assetId) {
        try {
            Integer maxNumber = inspectionRecordMapper.selectObjs(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<InspectionRecord>()
                    .eq(InspectionRecord::getAssetId, assetId)
                    .eq(InspectionRecord::getInspectionType, "对应性")
                    .select(InspectionRecord::getInspectionNumber)
                    .orderByDesc(InspectionRecord::getInspectionNumber)
                    .last("LIMIT 1")
            ).stream()
            .map(obj -> (Integer) obj)
            .findFirst()
            .orElse(0);

            return maxNumber + 1;

        } catch (Exception e) {
            log.warn("⚠️ 获取巡检序号失败: {}", e.getMessage());
            return 1;
        }
    }

    /**
     * 处理多次检测同一标线的情况
     * 现在每次检测都是独立记录，只需要简单去重即可
     */
    private boolean handleMultipleDetections(InspectionRecord inspection, String laneSpecificMarking,
                                           DetectionRecord detectionRecord, List<String> foundMarkings) {

        // 在同一次检测中，避免重复添加相同的车道标线
        if (foundMarkings.contains(laneSpecificMarking)) {
            log.debug("🔄 同一检测中已存在相同车道标线，跳过: {}", laneSpecificMarking);
            return false;
        }

        // 其他情况都接受，因为每次检测都是独立记录
        return true;
    }



    /**
     * 地面标线位置信息
     */
    @Data
    private static class GroundMarkingPosition {
        private String name;
        private double centerX;
        private double centerY;
        private double x1, y1, x2, y2;

        public GroundMarkingPosition(String name, double centerX, double centerY,
                                   double x1, double y1, double x2, double y2) {
            this.name = name;
            this.centerX = centerX;
            this.centerY = centerY;
            this.x1 = x1;
            this.y1 = y1;
            this.x2 = x2;
            this.y2 = y2;
        }
    }






}
