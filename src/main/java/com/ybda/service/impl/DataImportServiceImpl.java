package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.mapper.SyncRecordMapper;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.InspectionRecordMapper;
import com.ybda.mapper.AlertRecordMapper;
import com.ybda.model.entity.TrafficAsset;
import com.ybda.model.entity.SyncRecord;
import com.ybda.model.entity.DetectionRecord;
import com.ybda.model.entity.DetectionDetail;
import com.ybda.model.entity.InspectionRecord;
import com.ybda.model.entity.AlertRecord;
import com.ybda.service.DataImportService;
import com.ybda.utils.TimeUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 数据导入服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataImportServiceImpl implements DataImportService {

    private final TrafficAssetMapper trafficAssetMapper;
    private final SyncRecordMapper syncRecordMapper;
    private final DetectionRecordMapper detectionRecordMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final InspectionRecordMapper inspectionRecordMapper;
    private final AlertRecordMapper alertRecordMapper;
    private final ObjectMapper objectMapper;
    
    @Override
    @Transactional
    public Map<String, Object> importAssetData(String dataFilePath) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔄 开始导入完整数据: {}", dataFilePath);

            // 读取JSON文件
            String jsonContent = Files.readString(Paths.get(dataFilePath));
            Map<String, Object> data = objectMapper.readValue(jsonContent, new TypeReference<Map<String, Object>>() {});

            // 统计信息
            Map<String, Integer> stats = new HashMap<>();
            stats.put("assetSuccess", 0);
            stats.put("assetFailed", 0);
            stats.put("assetNew", 0);
            stats.put("assetUpdated", 0);
            stats.put("detectionSuccess", 0);
            stats.put("detectionFailed", 0);
            stats.put("inspectionSuccess", 0);
            stats.put("inspectionFailed", 0);
            stats.put("alertSuccess", 0);
            stats.put("alertFailed", 0);
            
            // 1. 处理新增资产数据
            if (data.containsKey("newAssets")) {
                log.info("📦 开始处理新增资产数据");
                List<Map<String, Object>> assets = (List<Map<String, Object>>) data.get("newAssets");

                for (Map<String, Object> assetData : assets) {
                    try {
                        TrafficAsset asset = convertMapToAsset(assetData);

                        // 检查资产是否已存在
                        TrafficAsset existingAsset = trafficAssetMapper.selectByAssetId(asset.getAssetId());

                        if (existingAsset != null) {
                            // 更新现有资产
                            asset.setId(existingAsset.getId());
                            asset.setUpdatedTime(TimeUtils.now());
                            trafficAssetMapper.updateById(asset);
                            stats.put("assetUpdated", stats.get("assetUpdated") + 1);
                            log.debug("📝 更新资产: {}", asset.getAssetId());
                        } else {
                            // 插入新资产
                            asset.setCreatedTime(TimeUtils.now());
                            asset.setUpdatedTime(TimeUtils.now());
                            trafficAssetMapper.insert(asset);
                            stats.put("assetNew", stats.get("assetNew") + 1);
                            log.debug("🆕 新增资产: {}", asset.getAssetId());
                        }

                        stats.put("assetSuccess", stats.get("assetSuccess") + 1);

                    } catch (Exception e) {
                        log.error("❌ 导入资产失败: {}", assetData.get("assetId"), e);
                        stats.put("assetFailed", stats.get("assetFailed") + 1);
                    }
                }
                log.info("✅ 资产数据处理完成: 成功{}个, 失败{}个, 新增{}个, 更新{}个",
                    stats.get("assetSuccess"), stats.get("assetFailed"),
                    stats.get("assetNew"), stats.get("assetUpdated"));
            }

            // 2. 处理检测记录数据
            if (data.containsKey("detectionRecords")) {
                log.info("📦 开始处理检测记录数据");
                List<Map<String, Object>> detectionRecords = (List<Map<String, Object>>) data.get("detectionRecords");

                for (Map<String, Object> recordData : detectionRecords) {
                    try {
                        DetectionRecord record = convertMapToDetectionRecord(recordData);

                        // 检查记录是否已存在（根据frameId和timestamp）
                        DetectionRecord existingRecord = detectionRecordMapper.selectOne(
                            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<DetectionRecord>()
                                .eq("frame_id", record.getFrameId())
                                .eq("timestamp", record.getTimestamp())
                        );

                        if (existingRecord == null) {
                            // 插入新记录
                            record.setCreatedTime(TimeUtils.now());
                            record.setUpdatedTime(TimeUtils.now());
                            detectionRecordMapper.insert(record);

                            // 处理检测详情
                            if (recordData.containsKey("details")) {
                                List<Map<String, Object>> details = (List<Map<String, Object>>) recordData.get("details");
                                for (Map<String, Object> detailData : details) {
                                    try {
                                        DetectionDetail detail = convertMapToDetectionDetail(detailData);
                                        detail.setDetectionRecordId(record.getId());
                                        detectionDetailMapper.insert(detail);
                                    } catch (Exception e) {
                                        log.warn("⚠️ 导入检测详情失败: recordId={}", record.getId(), e);
                                    }
                                }
                            }

                            stats.put("detectionSuccess", stats.get("detectionSuccess") + 1);
                            log.debug("🆕 新增检测记录: frameId={}", record.getFrameId());
                        } else {
                            log.debug("⏭️ 检测记录已存在，跳过: frameId={}", record.getFrameId());
                        }

                    } catch (Exception e) {
                        log.error("❌ 导入检测记录失败: {}", recordData.get("frameId"), e);
                        stats.put("detectionFailed", stats.get("detectionFailed") + 1);
                    }
                }
                log.info("✅ 检测记录处理完成: 成功{}个, 失败{}个",
                    stats.get("detectionSuccess"), stats.get("detectionFailed"));
            }
            
            // 3. 处理巡检记录数据
            if (data.containsKey("inspectionRecords")) {
                log.info("📦 开始处理巡检记录数据");
                List<Map<String, Object>> inspectionRecords = (List<Map<String, Object>>) data.get("inspectionRecords");

                for (Map<String, Object> recordData : inspectionRecords) {
                    try {
                        InspectionRecord record = convertMapToInspectionRecord(recordData);

                        // 检查记录是否已存在（根据assetId和inspectionTime）
                        InspectionRecord existingRecord = inspectionRecordMapper.selectOne(
                            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<InspectionRecord>()
                                .eq("asset_id", record.getAssetId())
                                .eq("inspection_time", record.getInspectionTime())
                        );

                        if (existingRecord == null) {
                            // 插入新记录
                            record.setCreatedTime(TimeUtils.now());
                            inspectionRecordMapper.insert(record);
                            stats.put("inspectionSuccess", stats.get("inspectionSuccess") + 1);
                            log.debug("🆕 新增巡检记录: assetId={}", record.getAssetId());
                        } else {
                            log.debug("⏭️ 巡检记录已存在，跳过: assetId={}", record.getAssetId());
                        }

                    } catch (Exception e) {
                        log.error("❌ 导入巡检记录失败: {}", recordData.get("assetId"), e);
                        stats.put("inspectionFailed", stats.get("inspectionFailed") + 1);
                    }
                }
                log.info("✅ 巡检记录处理完成: 成功{}个, 失败{}个",
                    stats.get("inspectionSuccess"), stats.get("inspectionFailed"));
            }

            // 4. 处理报警记录数据
            if (data.containsKey("alertRecords")) {
                log.info("📦 开始处理报警记录数据");
                List<Map<String, Object>> alertRecords = (List<Map<String, Object>>) data.get("alertRecords");

                for (Map<String, Object> recordData : alertRecords) {
                    try {
                        AlertRecord record = convertMapToAlertRecord(recordData);

                        // 检查记录是否已存在（根据alertType、assetId和createdTime）
                        AlertRecord existingRecord = alertRecordMapper.selectOne(
                            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<AlertRecord>()
                                .eq("alert_type", record.getAlertType())
                                .eq("asset_id", record.getAssetId())
                                .eq("created_time", record.getCreatedTime())
                        );

                        if (existingRecord == null) {
                            // 插入新记录
                            alertRecordMapper.insert(record);
                            stats.put("alertSuccess", stats.get("alertSuccess") + 1);
                            log.debug("🆕 新增报警记录: type={}, assetId={}", record.getAlertType(), record.getAssetId());
                        } else {
                            log.debug("⏭️ 报警记录已存在，跳过: type={}, assetId={}", record.getAlertType(), record.getAssetId());
                        }

                    } catch (Exception e) {
                        log.error("❌ 导入报警记录失败: {}", recordData.get("assetId"), e);
                        stats.put("alertFailed", stats.get("alertFailed") + 1);
                    }
                }
                log.info("✅ 报警记录处理完成: 成功{}个, 失败{}个",
                    stats.get("alertSuccess"), stats.get("alertFailed"));
            }

            // 汇总结果
            int totalSuccess = stats.get("assetSuccess") + stats.get("detectionSuccess") +
                              stats.get("inspectionSuccess") + stats.get("alertSuccess");
            int totalFailed = stats.get("assetFailed") + stats.get("detectionFailed") +
                             stats.get("inspectionFailed") + stats.get("alertFailed");

            result.put("success", true);
            result.put("totalCount", totalSuccess + totalFailed);
            result.put("successCount", totalSuccess);
            result.put("failedCount", totalFailed);
            result.put("newCount", stats.get("assetNew"));
            result.put("updatedCount", stats.get("assetUpdated"));
            result.put("detailStats", stats);
            result.put("message", "完整数据导入完成");

            log.info("✅ 完整数据导入完成: 总成功{}个, 总失败{}个", totalSuccess, totalFailed);
            
        } catch (Exception e) {
            log.error("❌ 导入资产数据失败", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> importSystemConfig(String configFilePath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导入系统配置: {}", configFilePath);
            
            // 读取配置文件
            String jsonContent = Files.readString(Paths.get(configFilePath));
            Map<String, Object> config = objectMapper.readValue(jsonContent, new TypeReference<Map<String, Object>>() {});
            
            // TODO: 实现系统配置的导入逻辑
            // 这里可以根据实际需求实现配置项的更新
            
            result.put("success", true);
            result.put("message", "系统配置导入完成");
            
            log.info("✅ 系统配置导入完成");
            
        } catch (Exception e) {
            log.error("❌ 导入系统配置失败", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> importVerificationRules(String rulesFilePath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导入验证规则: {}", rulesFilePath);
            
            // 读取规则文件
            String jsonContent = Files.readString(Paths.get(rulesFilePath));
            Map<String, Object> rules = objectMapper.readValue(jsonContent, new TypeReference<Map<String, Object>>() {});
            
            // TODO: 实现验证规则的导入逻辑
            // 这里可以根据实际需求实现规则的更新
            
            result.put("success", true);
            result.put("message", "验证规则导入完成");
            
            log.info("✅ 验证规则导入完成");
            
        } catch (Exception e) {
            log.error("❌ 导入验证规则失败", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> importImages(String zipFilePath, String targetDir) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始导入图片文件: {} 到 {}", zipFilePath, targetDir);
            
            // 创建目标目录
            Path targetPath = Paths.get(targetDir);
            if (!Files.exists(targetPath)) {
                Files.createDirectories(targetPath);
            }
            
            int successCount = 0;
            int failedCount = 0;
            long totalSize = 0;
            
            try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
                ZipEntry entry;
                byte[] buffer = new byte[8192];
                
                while ((entry = zis.getNextEntry()) != null) {
                    try {
                        if (entry.isDirectory()) {
                            continue;
                        }
                        
                        String fileName = entry.getName();
                        Path filePath = targetPath.resolve(fileName);
                        
                        // 确保父目录存在
                        Path parentDir = filePath.getParent();
                        if (parentDir != null && !Files.exists(parentDir)) {
                            Files.createDirectories(parentDir);
                        }
                        
                        // 解压文件
                        try (FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
                            int length;
                            while ((length = zis.read(buffer)) > 0) {
                                fos.write(buffer, 0, length);
                            }
                        }
                        
                        totalSize += entry.getSize();
                        successCount++;
                        log.debug("📷 解压图片: {}", fileName);
                        
                    } catch (Exception e) {
                        log.error("❌ 解压图片失败: {}", entry.getName(), e);
                        failedCount++;
                    } finally {
                        zis.closeEntry();
                    }
                }
            }
            
            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("totalSize", totalSize);
            result.put("targetDir", targetDir);
            result.put("message", "图片导入完成");
            
            log.info("✅ 图片导入完成: 成功{}个, 失败{}个, 总大小{}KB", 
                successCount, failedCount, totalSize / 1024);
            
        } catch (Exception e) {
            log.error("❌ 导入图片失败", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> validateImportData(String dataFilePath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始验证导入数据: {}", dataFilePath);
            
            // 检查文件是否存在
            if (!Files.exists(Paths.get(dataFilePath))) {
                result.put("success", false);
                result.put("message", "数据文件不存在");
                return result;
            }
            
            // 读取并验证JSON格式
            String jsonContent = Files.readString(Paths.get(dataFilePath));
            Map<String, Object> data = objectMapper.readValue(jsonContent, new TypeReference<Map<String, Object>>() {});
            
            // 验证数据结构
            List<String> validationErrors = new ArrayList<>();
            
            // 检查必需的字段
            if (!data.containsKey("exportTime")) {
                validationErrors.add("缺少导出时间字段");
            }
            
            if (!data.containsKey("dataStartTime") || !data.containsKey("dataEndTime")) {
                validationErrors.add("缺少数据时间范围字段");
            }
            
            // 验证资产数据
            if (data.containsKey("newAssets")) {
                List<Map<String, Object>> assets = (List<Map<String, Object>>) data.get("newAssets");
                for (int i = 0; i < assets.size(); i++) {
                    Map<String, Object> asset = assets.get(i);
                    if (!asset.containsKey("assetId")) {
                        validationErrors.add("资产[" + i + "]缺少assetId字段");
                    }
                    if (!asset.containsKey("type")) {
                        validationErrors.add("资产[" + i + "]缺少type字段");
                    }
                }
            }
            
            if (validationErrors.isEmpty()) {
                result.put("success", true);
                result.put("message", "数据验证通过");
            } else {
                result.put("success", false);
                result.put("message", "数据验证失败");
                result.put("errors", validationErrors);
            }
            
            log.info("✅ 数据验证完成: {}", validationErrors.isEmpty() ? "通过" : "失败");
            
        } catch (Exception e) {
            log.error("❌ 验证导入数据失败", e);
            result.put("success", false);
            result.put("message", "验证失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> performFullImport(String packagePath) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("🔄 开始执行完整导入: {}", packagePath);
            
            Path packageDir = Paths.get(packagePath);
            if (!Files.exists(packageDir) || !Files.isDirectory(packageDir)) {
                result.put("success", false);
                result.put("message", "导入包目录不存在");
                return result;
            }
            
            Map<String, Object> importResults = new HashMap<>();
            
            // 查找数据文件
            Optional<Path> dataFile = Files.list(packageDir)
                .filter(path -> path.getFileName().toString().endsWith(".json"))
                .findFirst();
            
            if (dataFile.isPresent()) {
                // 验证数据
                Map<String, Object> validationResult = validateImportData(dataFile.get().toString());
                if (!(Boolean) validationResult.get("success")) {
                    result.put("success", false);
                    result.put("message", "数据验证失败");
                    result.put("validationResult", validationResult);
                    return result;
                }
                
                // 导入资产数据
                Map<String, Object> assetResult = importAssetData(dataFile.get().toString());
                importResults.put("assets", assetResult);
            }
            
            // 查找图片文件
            Optional<Path> imageFile = Files.list(packageDir)
                .filter(path -> path.getFileName().toString().endsWith(".zip"))
                .findFirst();
            
            if (imageFile.isPresent()) {
                // 导入图片
                String imageTargetDir = "D:\\资产管理图片文件\\imported\\";
                Map<String, Object> imageResult = importImages(imageFile.get().toString(), imageTargetDir);
                importResults.put("images", imageResult);
            }
            
            result.put("success", true);
            result.put("message", "完整导入完成");
            result.put("results", importResults);
            
            log.info("✅ 完整导入完成");
            
        } catch (Exception e) {
            log.error("❌ 执行完整导入失败", e);
            result.put("success", false);
            result.put("message", "导入失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 将Map转换为TrafficAsset对象
     */
    private TrafficAsset convertMapToAsset(Map<String, Object> assetData) {
        try {
            String json = objectMapper.writeValueAsString(assetData);
            return objectMapper.readValue(json, TrafficAsset.class);
        } catch (Exception e) {
            log.error("❌ Map转换为TrafficAsset失败", e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 将Map转换为DetectionRecord对象
     */
    private DetectionRecord convertMapToDetectionRecord(Map<String, Object> recordData) {
        try {
            String json = objectMapper.writeValueAsString(recordData);
            return objectMapper.readValue(json, DetectionRecord.class);
        } catch (Exception e) {
            log.error("❌ Map转换为DetectionRecord失败", e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 将Map转换为DetectionDetail对象
     */
    private DetectionDetail convertMapToDetectionDetail(Map<String, Object> detailData) {
        try {
            String json = objectMapper.writeValueAsString(detailData);
            return objectMapper.readValue(json, DetectionDetail.class);
        } catch (Exception e) {
            log.error("❌ Map转换为DetectionDetail失败", e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 将Map转换为InspectionRecord对象
     */
    private InspectionRecord convertMapToInspectionRecord(Map<String, Object> recordData) {
        try {
            String json = objectMapper.writeValueAsString(recordData);
            return objectMapper.readValue(json, InspectionRecord.class);
        } catch (Exception e) {
            log.error("❌ Map转换为InspectionRecord失败", e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 将Map转换为AlertRecord对象
     */
    private AlertRecord convertMapToAlertRecord(Map<String, Object> recordData) {
        try {
            String json = objectMapper.writeValueAsString(recordData);
            return objectMapper.readValue(json, AlertRecord.class);
        } catch (Exception e) {
            log.error("❌ Map转换为AlertRecord失败", e);
            throw new RuntimeException("数据转换失败", e);
        }
    }
}
