package com.ybda.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.mapper.DetectionRecordMapper;
import com.ybda.mapper.DetectionDetailMapper;
import com.ybda.mapper.ImageReviewTaskMapper;
import com.ybda.mapper.PendingAssetCheckMapper;
import com.ybda.mapper.TrafficAssetMapper;
import com.ybda.model.dto.CoordinatePoint;
import com.ybda.utils.GeometryTypeHelper;
import com.ybda.service.SignGroundMatchingService;
import com.ybda.service.AssetIntegrityService;
import com.ybda.service.AssetInspectionService;
import com.ybda.model.dto.DetectionRequestDTO;
import com.ybda.model.dto.SignDetectionDTO;
import com.ybda.model.entity.*;
import com.ybda.service.DetectionService;
import com.ybda.service.LocationCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 检测服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectionServiceImpl implements DetectionService {
    
    private final DetectionRecordMapper detectionRecordMapper;
    private final DetectionDetailMapper detectionDetailMapper;
    private final ImageReviewTaskMapper imageReviewTaskMapper;
    private final PendingAssetCheckMapper pendingAssetCheckMapper;
    private final TrafficAssetMapper trafficAssetMapper;
    private final LocationCacheService locationCacheService;
    private final SignGroundMatchingService signGroundMatchingService;
    private final AssetIntegrityService assetIntegrityService;
    private final AssetInspectionService assetInspectionService;
    private final ObjectMapper objectMapper;
    
    // 配置参数
    private static final double MATCHING_DISTANCE = 5.0; // 5米匹配距离（点状资产，提高精度）
    private static final double LINE_ASSET_MATCHING_DISTANCE = 100.0; // 100米匹配距离（线状资产）
    private static final int TIME_WINDOW_MINUTES = 5; // 5分钟时间窗口
    
    @Override
    @Transactional
    public Map<String, Object> processDetectionRequest(DetectionRequestDTO detectionRequest) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🚀 开始处理检测请求: frameId={}, timestamp={}, signsCount={}",
                detectionRequest.getFrameId(), detectionRequest.getTimestamp(),
                detectionRequest.getSigns() != null ? detectionRequest.getSigns().size() : 0);

            // 1. 获取当前GPS位置
            log.info("📍 步骤1: 获取当前GPS位置");
            DeviceLocation currentLocation = locationCacheService.getLatestLocationFromCache();

            if (currentLocation != null) {
                log.info("✅ GPS位置获取成功: lat={}, lng={}, time={}",
                    currentLocation.getLat(), currentLocation.getLng(), currentLocation.getDeviceTime());
            } else {
                log.warn("⚠️ 未获取到GPS位置数据，将使用空位置信息");
            }

            // 2. 创建检测记录
            log.info("📝 步骤2: 创建检测记录");
            DetectionRecord detectionRecord = createDetectionRecord(detectionRequest, currentLocation);
            int recordResult = detectionRecordMapper.insert(detectionRecord);

            if (recordResult <= 0) {
                throw new RuntimeException("保存检测记录失败");
            }
            log.info("✅ 检测记录创建成功: recordId={}, gpsLocation=({}, {})",
                detectionRecord.getId(), detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());

            // 3. 创建检测详情
            log.info("📋 步骤3: 创建检测详情");
            List<DetectionDetail> detectionDetails = createDetectionDetails(
                detectionRecord.getId(), detectionRequest.getSigns());

            if (!detectionDetails.isEmpty()) {
                detectionDetailMapper.batchInsert(detectionDetails);
                log.info("✅ 检测详情创建成功: {} 条详情记录", detectionDetails.size());

                // 打印每个检测详情
                for (DetectionDetail detail : detectionDetails) {
                    log.info("  - 检测到: type={}, name={}, classId={}, trackId={}",
                        detail.getType(), detail.getName(), detail.getClassId(), detail.getTrackId());
                }
            } else {
                log.info("ℹ️ 无检测详情需要创建");
            }

            // 4. 确认待确认的资产检查记录
            log.info("🔍 步骤4: 确认待确认的资产检查记录");
            confirmPendingAssetChecks(detectionRecord);

            // 5. 处理新增资产
            log.info("🆕 步骤5: 处理新增资产");
            processNewAssets(detectionRecord);

            // 6. 建立检测记录与资产的对应关系
            log.debug("🔗 步骤6: 建立检测记录与资产的对应关系");
            establishAssetRelations(detectionRecord, detectionDetails);

            // 7. 处理标牌地面对应关系验证
            log.debug("🏷️ 步骤7: 处理标牌地面对应关系验证");
            processSignGroundMatching(detectionRecord, detectionDetails);

            // 8. 处理完整性验证
            log.debug("🔍 步骤8: 处理完整性验证");
            assetIntegrityService.processDetectionResult(detectionRecord);

            // 9. 处理资产存在性巡检
            log.debug("📋 步骤9: 处理资产存在性巡检");
            assetInspectionService.processAssetExistenceInspections(detectionRecord);

            // 10. 更新处理状态
            log.info("✏️ 步骤10: 更新处理状态为已处理");
            detectionRecordMapper.updateProcessStatus(
                detectionRecord.getId(),
                "已处理");

            result.put("success", true);
            result.put("recordId", detectionRecord.getId());
            result.put("detailsCount", detectionDetails.size());
            result.put("imageUrl", detectionRecord.getImageUrl());
            result.put("message", "检测请求处理成功");

            log.info("🎉 检测请求处理完成: frameId={}, recordId={}, detailsCount={}, imageUrl={}",
                detectionRequest.getFrameId(), detectionRecord.getId(), detectionDetails.size(),
                detectionRecord.getImageUrl() != null ? "已保存" : "无");

        } catch (Exception e) {
            log.error("❌ 处理检测请求失败: frameId={}", detectionRequest.getFrameId(), e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }
    
    /**
     * 创建检测记录
     */
    private DetectionRecord createDetectionRecord(DetectionRequestDTO request, DeviceLocation location) {
        DetectionRecord record = new DetectionRecord();
        record.setFrameId(request.getFrameId());
        record.setTimestamp(request.getTimestamp());
        record.setImageUrl(request.getImageUrl());
        record.setProcessStatus("待处理");
        record.setCreatedTime(LocalDateTime.now());
        
        // 设置GPS位置
        if (location != null && location.getLat() != null && location.getLng() != null) {
            record.setGpsLatitude(location.getLat());
            record.setGpsLongitude(location.getLng());
            record.setDeviceId(location.getDeviceId()); // 默认设备ID
        }
        
        // 保存原始JSON数据
        try {
            record.setRawData(objectMapper.writeValueAsString(request));
        } catch (Exception e) {
            log.warn("序列化原始数据失败", e);
        }
        
        return record;
    }
    
    /**
     * 创建检测详情列表
     */
    private List<DetectionDetail> createDetectionDetails(Long recordId, List<SignDetectionDTO> signs) {
        List<DetectionDetail> details = new ArrayList<>();
        
        for (SignDetectionDTO sign : signs) {
            DetectionDetail detail = new DetectionDetail();
            detail.setDetectionRecordId(recordId);
            detail.setType(sign.getType());
            detail.setName(sign.getName());
            detail.setModelSource(sign.getModelSource());
            detail.setClassId(sign.getClassId());
            detail.setTrackId(sign.getTrackId());
            detail.setCreatedTime(LocalDateTime.now());
            
            // 设置边界框坐标
            if (sign.getBbox() != null && sign.getBbox().size() >= 4) {
                detail.setBboxX1(sign.getBbox().get(0));
                detail.setBboxY1(sign.getBbox().get(1));
                detail.setBboxX2(sign.getBbox().get(2));
                detail.setBboxY2(sign.getBbox().get(3));
            }
            
            details.add(detail);
        }
        
        return details;
    }
    
    @Override
    public void confirmPendingAssetChecks(DetectionRecord detectionRecord) {
        log.info("🔍 开始确认待确认记录: recordId={}", detectionRecord.getId());

        if (!detectionRecord.hasValidGpsLocation()) {
            log.warn("⚠️ 检测记录缺少有效GPS位置，无法确认待确认记录: recordId={}", detectionRecord.getId());
            return;
        }

        try {
            // 计算时间窗口
            LocalDateTime detectionTime = detectionRecord.getCreatedTime();
            LocalDateTime startTime = detectionTime.minusMinutes(TIME_WINDOW_MINUTES);
            LocalDateTime endTime = detectionTime.plusMinutes(TIME_WINDOW_MINUTES);

            log.info("📅 时间窗口: {} ~ {} ({}分钟窗口)", startTime, endTime, TIME_WINDOW_MINUTES);
            log.info("📍 搜索位置: lat={}, lng={}, 距离={}米",
                detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude(), MATCHING_DISTANCE);

            // 查找附近的待确认记录按时间
            List<PendingAssetCheck> nearbyChecks = pendingAssetCheckMapper.searchForNearbyRecordsToBeConfirmedByTime(
                detectionRecord.getGpsLatitude(),
                detectionRecord.getGpsLongitude(),
                MATCHING_DISTANCE,
                startTime,
                endTime
            );

            log.info("🔍 找到附近的待确认记录: {} 条", nearbyChecks.size());

            if (nearbyChecks.isEmpty()) {
                log.info("ℹ️ 未找到匹配的待确认记录: recordId={}", detectionRecord.getId());
                return;
            }

            // 获取检测详情，用于匹配资产类型
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());

            Set<String> detectedTypes = new HashSet<>();
            for (DetectionDetail detail : detectionDetails) {
                detectedTypes.add(detail.getType());
            }

            log.info("🏷️ 检测到的资产类型: {}", detectedTypes);

            // 确认匹配的待确认记录
            List<Long> confirmedIds = new ArrayList<>();
            for (PendingAssetCheck check : nearbyChecks) {
                log.info("🔄 检查待确认记录: checkId={}, assetId={}, assetType={}, 经过时间={}",
                    check.getId(), check.getExpectedAssetId(), check.getAssetType(), check.getPassedTime());

                if (detectedTypes.contains(check.getAssetType())) {
                    confirmedIds.add(check.getId());
                    log.info("✅ 匹配成功，确认待确认记录: checkId={}, assetId={}, assetType={}",
                        check.getId(), check.getExpectedAssetId(), check.getAssetType());
                } else {
                    log.info("❌ 类型不匹配: 期望={}, 检测到={}", check.getAssetType(), detectedTypes);
                }
            }

            if (!confirmedIds.isEmpty()) {
                pendingAssetCheckMapper.batchConfirmChecks(
                    confirmedIds, detectionRecord.getId(), LocalDateTime.now());
                log.info("🎉 成功确认待确认记录: {} 条", confirmedIds.size());
            } else {
                log.info("ℹ️ 没有匹配的待确认记录需要确认");
            }

        } catch (Exception e) {
            log.error("❌ 确认待确认记录失败: recordId={}", detectionRecord.getId(), e);
        }
    }
    
    @Override
    public void processNewAssets(DetectionRecord detectionRecord) {
        if (!detectionRecord.hasValidGpsLocation()) {
            log.warn("⚠️ GPS位置无效，跳过新增资产处理");
            return;
        }

        try {
            // 获取检测详情
            List<DetectionDetail> detectionDetails = detectionDetailMapper
                .selectByDetectionRecordId(detectionRecord.getId());

            log.info("🔍 开始处理新增资产: 检测到{}个详情记录", detectionDetails.size());

            for (DetectionDetail detail : detectionDetails) {
                log.info("🔍 处理检测详情: type={}, name={}", detail.getType(), detail.getName());
                // 🔍 根据资产类型选择合适的匹配距离
                String geometryType = GeometryTypeHelper.inferFromName(detail.getType(), detail.getName());
                double searchDistance = GeometryTypeHelper.isLineAsset(geometryType) || GeometryTypeHelper.isPolygonAsset(geometryType)
                    ? LINE_ASSET_MATCHING_DISTANCE
                    : MATCHING_DISTANCE;

                log.info("🔍 搜索{}资产: type={}, name={}, 搜索距离={}米",
                    geometryType, detail.getType(), detail.getName(), searchDistance);

                // 🔍 对于线状资产，使用智能匹配逻辑
                if (GeometryTypeHelper.isLineAsset(geometryType)) {
                    TrafficAsset similarAsset = findSimilarLineAsset(detail, detectionRecord);
                    if (similarAsset != null) {
                        // 找到相似的线状资产，合并坐标
                        mergeCoordinateToAsset(similarAsset, detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
                        detectionDetailMapper.updateTrafficAssetId(detail.getId(), similarAsset.getId());

                        // 更新检测次数、最后检测时间和图片URL
                        trafficAssetMapper.updateDetectionInfoWithImage(
                            similarAsset.getAssetId(),
                            detectionRecord.getCreatedTime(),
                            similarAsset.getDetectionCount() + 1,
                            detectionRecord.getImageUrl()
                        );

                        log.info("🔗 合并到现有线状资产: assetId={}, name={}",
                            similarAsset.getAssetId(), similarAsset.getName());
                        continue; // 跳过创建新资产
                    }
                }

                // 查找附近是否已有相同类型的资产（用于点状资产）
                List<TrafficAsset> nearbyAssets = trafficAssetMapper.findNearbyAssets(
                    detectionRecord.getGpsLatitude(),
                    detectionRecord.getGpsLongitude(),
                    searchDistance
                );

                boolean assetExists = nearbyAssets.stream()
                    .anyMatch(asset -> asset.getType().equals(detail.getType())
                        && asset.getName().equals(detail.getName()));

                if (!assetExists) {
                    // 创建新资产
                    TrafficAsset newAsset = createNewAsset(detectionRecord, detail);
                    trafficAssetMapper.insert(newAsset);

                    // 关联检测详情
                    detectionDetailMapper.updateTrafficAssetId(detail.getId(), newAsset.getId());

                    // 创建已确认的检查记录（因为是刚检测到的，说明资产确实存在）
                    createConfirmedAssetCheck(detectionRecord, newAsset);

                    log.info("🆕 发现新资产: type={}, name={}, geometryType={}, location=({}, {})",
                        newAsset.getType(), newAsset.getName(), geometryType,
                        newAsset.getLatitude(), newAsset.getLongitude());
                } else {
                    // 从多个匹配的资产中选择距离最近的一个
                    TrafficAsset existingAsset = findClosestMatchingAsset(
                        nearbyAssets,
                        detail.getType(),
                        detail.getName(),
                        detectionRecord.getGpsLatitude(),
                        detectionRecord.getGpsLongitude()
                    );
                    
                    if (existingAsset != null) {
                        // 🔗 对于线状/面状资产，合并新的GPS坐标点
                        if (detectionRecord.hasValidGpsLocation()) {
                            String existingGeometryType = existingAsset.getGeometryType();

                            if (GeometryTypeHelper.isLineAsset(existingGeometryType) || GeometryTypeHelper.isPolygonAsset(existingGeometryType)) {
                                mergeCoordinateToAsset(existingAsset, detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
                            }
                        }

                        // 处理图片质量审核逻辑
                        handleImageQualityReview(existingAsset, detectionRecord);

                        // 关联检测详情
                        detectionDetailMapper.updateTrafficAssetId(detail.getId(), existingAsset.getId());

                        log.info("🔄 更新现有资产: assetId={}, geometryType={}",
                            existingAsset.getAssetId(), existingAsset.getGeometryType());
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("处理新增资产失败: recordId={}", detectionRecord.getId(), e);
        }
    }
    
    /**
     * 创建新资产（支持智能几何类型推断）
     */
    private TrafficAsset createNewAsset(DetectionRecord detectionRecord, DetectionDetail detail) {
        TrafficAsset asset = new TrafficAsset();
        asset.setAssetId(generateAssetId(detail.getType()));
        asset.setType(detail.getType());
        asset.setName(detail.getName());

        // 🔍 智能推断几何类型
        String geometryType = GeometryTypeHelper.inferFromName(detail.getType(), detail.getName());
        asset.setGeometryType(geometryType);

        log.info("🔍 智能推断几何类型: type={}, name={} → geometryType={}",
            detail.getType(), detail.getName(), geometryType);

        // 📍 根据几何类型设置坐标
        if (detectionRecord.hasValidGpsLocation()) {
            if (GeometryTypeHelper.isPointAsset(geometryType)) {
                // 点状资产：直接使用GPS坐标
                asset.setLatitude(detectionRecord.getGpsLatitude());
                asset.setLongitude(detectionRecord.getGpsLongitude());
                log.debug("📍 点状资产坐标: ({}, {})",
                    detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
            } else {
                // 线状/面状资产：初始化为单点，后续会在updateExistingAsset中扩展
                String coordinates = String.format(
                    "[{\"lat\":%.6f,\"lng\":%.6f}]",
                    detectionRecord.getGpsLatitude(),
                    detectionRecord.getGpsLongitude()
                );
                asset.setGeometryCoordinates(coordinates);
                log.info("📍 {}初始坐标: {}", geometryType, coordinates);
            }
        }

        asset.setFirstDetectedTime(detectionRecord.getCreatedTime());
        asset.setLastDetectedTime(detectionRecord.getCreatedTime());
        asset.setDetectionCount(1);
        asset.setStatus("正常");
        asset.setAvailable(1);

        // 💡 设置检测图片，状态为待审核
        asset.setDetectionImageUrl(detectionRecord.getImageUrl());
        asset.setImageQualityStatus("待审核");

        asset.setCreatedTime(LocalDateTime.now());
        asset.setUpdatedTime(LocalDateTime.now());

        return asset;
    }
    
    /**
     * 生成资产ID
     */
    private String generateAssetId(String type) {
        String prefix = switch (type) {
            case "ground_marking" -> "GM";
            case "overhead_sign" -> "OS";
            case "traffic_light" -> "TL";
            case "barrier" -> "BR";
            default -> "AS";
        };
        return prefix + "_" + System.currentTimeMillis();
    }

    /**
     * 查找相似的线状资产（用于智能合并）
     */
    private TrafficAsset findSimilarLineAsset(DetectionDetail detail, DetectionRecord detectionRecord) {
        try {
            // 直接查找同类型同名称的线状资产（不依赖GPS距离）
            List<TrafficAsset> lineAssets = trafficAssetMapper.findLineAssetsByTypeAndName(
                detail.getType(),
                detail.getName()
            );

            log.info("🔍 找到同类型同名称的线状资产: {}个", lineAssets.size());

            if (lineAssets.isEmpty()) {
                log.info("🔍 未找到匹配的线状资产: type={}, name={}", detail.getType(), detail.getName());
                return null;
            }

            // 如果只有一个匹配的线状资产，直接返回
            if (lineAssets.size() == 1) {
                log.info("🔍 找到唯一匹配的线状资产: {}", lineAssets.get(0).getAssetId());
                return lineAssets.get(0);
            }

            // 如果有多个，选择最近的一个
            CoordinatePoint newPoint = new CoordinatePoint(detectionRecord.getGpsLatitude(), detectionRecord.getGpsLongitude());
            TrafficAsset closestAsset = null;
            double minDistance = Double.MAX_VALUE;

            for (TrafficAsset asset : lineAssets) {
                double distance = calculateDistanceToLineAsset(newPoint, asset);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestAsset = asset;
                }
            }

            log.info("🔍 从{}个候选资产中选择最近的: assetId={}, 距离={}米",
                lineAssets.size(), closestAsset != null ? closestAsset.getAssetId() : "null", minDistance);

            return closestAsset;

        } catch (Exception e) {
            log.error("❌ 查找相似线状资产失败", e);
            return null;
        }
    }

    /**
     * 计算点到线状资产的最短距离
     */
    private double calculateDistanceToLineAsset(CoordinatePoint point, TrafficAsset lineAsset) {
        try {
            if (lineAsset.getGeometryCoordinates() == null) {
                // 如果没有坐标数据，使用资产的中心点距离
                if (lineAsset.getLatitude() != null && lineAsset.getLongitude() != null) {
                    CoordinatePoint assetPoint = new CoordinatePoint(lineAsset.getLatitude(), lineAsset.getLongitude());
                    return point.distanceTo(assetPoint);
                }
                return Double.MAX_VALUE;
            }

            List<CoordinatePoint> linePoints = objectMapper.readValue(
                lineAsset.getGeometryCoordinates(),
                new TypeReference<>() {}
            );

            if (linePoints.isEmpty()) {
                return Double.MAX_VALUE;
            }

            // 计算到线上所有点的最短距离
            return linePoints.stream()
                .mapToDouble(point::distanceTo)
                .min()
                .orElse(Double.MAX_VALUE);

        } catch (Exception e) {
            log.error("❌ 计算到线状资产距离失败: assetId={}", lineAsset.getAssetId(), e);
            return Double.MAX_VALUE;
        }
    }

    /**
     * 将新的坐标点合并到线状/面状资产中
     */
    private void mergeCoordinateToAsset(TrafficAsset asset, Double newLat, Double newLng) {
        try {
            List<CoordinatePoint> existingPoints = new ArrayList<>();

            // 解析现有坐标
            if (asset.getGeometryCoordinates() != null && !asset.getGeometryCoordinates().trim().isEmpty()) {
                existingPoints = objectMapper.readValue(
                    asset.getGeometryCoordinates(),
                        new TypeReference<>() {
                        }
                );
            }

            CoordinatePoint newPoint = new CoordinatePoint(newLat, newLng);

            // 检查新点是否与现有点重复（距离小于5米认为是重复）
            boolean isDuplicate = existingPoints.stream()
                .anyMatch(point -> point.distanceTo(newPoint) < 5.0);

            if (!isDuplicate) {
                existingPoints.add(newPoint);

                // 按照合理的顺序排序坐标点（可以根据实际需求调整排序逻辑）
                sortCoordinatePoints(existingPoints);

                // 更新资产的坐标数据
                String updatedCoordinates = objectMapper.writeValueAsString(existingPoints);
                asset.setGeometryCoordinates(updatedCoordinates);

                // 直接更新数据库中的坐标数据
                trafficAssetMapper.updateGeometryCoordinates(asset.getAssetId(), updatedCoordinates);

                log.info("🔗 {}坐标合并: 新增点({}, {}), 总点数: {}",
                    asset.getGeometryType(), newLat, newLng, existingPoints.size());
            } else {
                log.debug("⚠️ 坐标点重复，跳过合并: ({}, {})", newLat, newLng);
            }

        } catch (Exception e) {
            log.error("❌ 合并坐标点失败: assetId={}", asset.getAssetId(), e);
        }
    }

    /**
     * 对坐标点进行排序（简单的从西到东、从南到北排序）
     * 实际项目中可能需要更复杂的排序逻辑，比如按照道路方向
     */
    private void sortCoordinatePoints(List<CoordinatePoint> points) {
        // 先按纬度排序（南到北）
        // 纬度相同时按经度排序（西到东）
        points.sort(Comparator.comparingDouble(CoordinatePoint::getLat).thenComparingDouble(CoordinatePoint::getLng));
    }

    /**
     * 建立检测记录与资产的对应关系
     * 直接使用已有的检测详情，避免重复数据库查询
     */
    private void establishAssetRelations(DetectionRecord detectionRecord, List<DetectionDetail> detectionDetails) {
        try {
            if (detectionDetails.isEmpty()) {
                log.debug("ℹ️ 无检测详情，跳过资产关联");
                return;
            }

            // 收集所有已关联的资产ID
            List<String> relatedAssetIds = new ArrayList<>();

            for (DetectionDetail detail : detectionDetails) {
                // 检测详情在processNewAssets步骤中已经关联了资产ID
                if (detail.getTrafficAssetId() != null) {
                    // 根据资产表ID获取资产ID
//                    TrafficAsset asset = trafficAssetMapper.selectById(detail.getTrafficAssetId());
//                    if (asset != null) {
                        relatedAssetIds.add(String.valueOf(detail.getTrafficAssetId()));
                        log.debug("✅ 收集资产关联: 检测详情[{}] -> 资产[{}] ({})",
                            detail.getName(), detail.getTrafficAssetId(), detail.getName());
//                    }
                }
            }

            // 记录资产关联信息（仅用于日志）
            if (!relatedAssetIds.isEmpty()) {
                log.info("🔗 检测记录资产关联完成: recordId={}, relatedAssets={}",
                    detectionRecord.getId(), relatedAssetIds);
            } else {
                log.debug("ℹ️ 无资产关联信息");
            }

        } catch (Exception e) {
            log.error("❌ 建立资产关联失败: recordId={}", detectionRecord.getId(), e);
        }
    }

    /**
     * 处理标牌地面对应关系验证
     * 只处理地面标线检测，天空标牌的处理已移动到步骤9的资产存在性巡检中
     */
    private void processSignGroundMatching(DetectionRecord detectionRecord, List<DetectionDetail> detectionDetails) {
        try {
            for (DetectionDetail detail : detectionDetails) {
                if ("ground_marking".equals(detail.getType())) {
                    // 处理地面标线检测
                    signGroundMatchingService.processGroundMarkingDetection(detectionRecord, detail.getName());
                    log.debug("🛣️ 处理地面标线: name={}", detail.getName());
                }
                // 注意：天空标牌的处理已移动到步骤9的资产存在性巡检中
            }

        } catch (Exception e) {
            log.error("❌ 处理标牌地面对应关系验证失败: recordId={}", detectionRecord.getId(), e);
        }
    }

    /**
     * 为新检测到的资产创建已确认的检查记录
     * 由于是刚检测到的资产，说明它确实存在，可以直接标记为已确认
     */
    private void createConfirmedAssetCheck(DetectionRecord detectionRecord, TrafficAsset newAsset) {
        try {
            // 检查是否已存在近期的检查记录（避免重复创建）
            if (hasRecentAssetCheck(detectionRecord.getDeviceId(), newAsset.getAssetId())) {
                log.debug("🔍 新资产已有近期检查记录，跳过创建: assetId={}", newAsset.getAssetId());
                return;
            }

            PendingAssetCheck check = new PendingAssetCheck();
            check.setDeviceId(detectionRecord.getDeviceId());
            check.setGpsTrackId(detectionRecord.getFrameId()); // 使用frameId作为轨迹标识
            check.setExpectedAssetId(newAsset.getAssetId());
            check.setAssetType(newAsset.getType());
            check.setAssetName(newAsset.getName());
            check.setGpsLatitude(detectionRecord.getGpsLatitude());
            check.setGpsLongitude(detectionRecord.getGpsLongitude());
            check.setPassedTime(detectionRecord.getCreatedTime());
            check.setCheckType("存在性");
            check.setStatus("已确认"); // 直接设为已确认
            check.setConfirmedDetectionId(detectionRecord.getId());
            check.setConfirmedTime(detectionRecord.getCreatedTime());
            check.setCreatedTime(LocalDateTime.now());

            pendingAssetCheckMapper.insert(check);

            log.info("✅ 为新资产创建已确认检查记录: assetId={}, assetName='{}'",
                newAsset.getAssetId(), newAsset.getName());

        } catch (Exception e) {
            log.error("❌ 创建新资产检查记录失败: assetId={}", newAsset.getAssetId(), e);
        }
    }

    /**
     * 检查是否已存在近期的资产检查记录
     */
    private boolean hasRecentAssetCheck(String deviceId, String assetId) {
        try {
            LocalDateTime recentTime = LocalDateTime.now().minusSeconds(10); // 10秒内

            // 查询近期是否已有该资产的检查记录
            List<PendingAssetCheck> recentChecks = pendingAssetCheckMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<PendingAssetCheck>()
                    .eq("device_id", deviceId)
                    .eq("expected_asset_id", assetId)
                    .ge("created_time", recentTime)
                    .orderByDesc("created_time")
                    .last("LIMIT 1")
            );

            if (recentChecks.isEmpty()) {
                return false; // 没有近期记录
            }

            PendingAssetCheck recentCheck = recentChecks.get(0);

            // 计算时间差（秒）
            long secondsDiff = java.time.Duration.between(recentCheck.getCreatedTime(), LocalDateTime.now()).getSeconds();

            if (secondsDiff > 7) {
                // 超过7秒，认为是新资产，不合并
                log.debug("🆕 超过7秒阈值，认为是新资产: deviceId={}, assetId={}, 时间差={}秒", deviceId, assetId, secondsDiff);
                return false;
            } else {
                // 7秒内，认为是同一资产的重复检测，合并
                log.debug("🔄 7秒内重复检测，合并处理: deviceId={}, assetId={}, 时间差={}秒", deviceId, assetId, secondsDiff);
                return true;
            }

        } catch (Exception e) {
            log.warn("⚠️ 检查近期资产记录失败: deviceId={}, assetId={}", deviceId, assetId, e);
            return false; // 出错时允许创建
        }
    }

    /**
     * 从多个匹配的资产中选择距离最近的一个
     * 方案1+3: 距离选择逻辑 + 提高匹配精度
     */
    private TrafficAsset findClosestMatchingAsset(List<TrafficAsset> nearbyAssets,
                                                  String targetType,
                                                  String targetName,
                                                  Double gpsLat,
                                                  Double gpsLng) {
        if (nearbyAssets == null || nearbyAssets.isEmpty() || gpsLat == null || gpsLng == null) {
            return null;
        }

        // 过滤出匹配类型和名称的资产
        List<TrafficAsset> matchingAssets = nearbyAssets.stream()
            .filter(asset -> asset.getType().equals(targetType) && asset.getName().equals(targetName))
            .toList();

        if (matchingAssets.isEmpty()) {
            log.debug("🔍 未找到匹配的资产: type={}, name={}", targetType, targetName);
            return null;
        }

        if (matchingAssets.size() == 1) {
            log.debug("🔍 找到唯一匹配资产: assetId={}", matchingAssets.get(0).getAssetId());
            return matchingAssets.get(0);
        }

        // 多个匹配资产，选择距离最近的
        TrafficAsset closestAsset = null;
        double minDistance = Double.MAX_VALUE;

        for (TrafficAsset asset : matchingAssets) {
            if (asset.getLatitude() == null || asset.getLongitude() == null) {
                log.warn("⚠️ 资产缺少坐标信息: assetId={}", asset.getAssetId());
                continue;
            }

            double distance = calculateDistance(gpsLat, gpsLng, asset.getLatitude(), asset.getLongitude());

            log.debug("🔍 资产距离计算: assetId={}, 距离={}米", asset.getAssetId(), distance);

            if (distance < minDistance) {
                minDistance = distance;
                closestAsset = asset;
            }
        }

        if (closestAsset != null) {
            log.info("🎯 从{}个候选资产中选择最近的: assetId={}, 距离={}米",
                matchingAssets.size(), closestAsset.getAssetId(), minDistance);
        }

        return closestAsset;
    }

    /**
     * 计算两个GPS坐标之间的距离（米）
     */
    private double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
        final double R = 6371000; // 地球半径（米）

        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLngRad = Math.toRadians(lng2 - lng1);

        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 处理图片质量审核逻辑
     */
    private void handleImageQualityReview(TrafficAsset asset, DetectionRecord detectionRecord) {
        try {
            String currentStatus = asset.getImageQualityStatus();

            if ("审核通过".equals(currentStatus)) {
                // 当前图片已审核通过，保留原图片，只更新检测信息
                log.info("📷 保留已审核通过的图片: assetId={}, 图片={}",
                    asset.getAssetId(), asset.getDetectionImageUrl());

                trafficAssetMapper.updateDetectionInfo(
                    asset.getAssetId(),
                    detectionRecord.getCreatedTime(),
                    asset.getDetectionCount() + 1
                );

            } else {
                // 当前图片未审核通过或待审核，使用新图片并重新审核
                log.info("🔄 使用新图片替换: assetId={}, 原图片状态={}, 新图片={}",
                    asset.getAssetId(), currentStatus, detectionRecord.getImageUrl());

                trafficAssetMapper.updateDetectionInfoWithImage(
                    asset.getAssetId(),
                    detectionRecord.getCreatedTime(),
                    asset.getDetectionCount() + 1,
                    detectionRecord.getImageUrl()
                );

                // 创建图片质量审核任务
                createImageQualityReviewTask(asset, detectionRecord);
            }

        } catch (Exception e) {
            log.error("❌ 处理图片质量审核失败: assetId={}", asset.getAssetId(), e);
        }
    }

    /**
     * 创建图片质量审核任务
     */
    private void createImageQualityReviewTask(TrafficAsset asset, DetectionRecord detectionRecord) {
        try {
            log.info("📋 创建图片质量审核任务: assetId={}", asset.getAssetId());
            log.info("   📍 资产位置: ({}, {})", asset.getLatitude(), asset.getLongitude());
            log.info("   📷 检测图片: {}", detectionRecord.getImageUrl());
            log.info("   🔍 需要人工审核图片质量是否符合要求");

            // 创建图片审核任务记录
            ImageReviewTask task = new ImageReviewTask();
            task.setAssetId(asset.getAssetId());
            task.setNewImageUrl(detectionRecord.getImageUrl());
            task.setDetectionRecordId(detectionRecord.getId());
            task.setReviewStatus("待审核");
            task.setCreatedTime(java.time.LocalDateTime.now());

            int inserted = imageReviewTaskMapper.insert(task);

            if (inserted > 0) {
                log.info("✅ 图片质量审核任务创建成功: taskId={}", task.getId());
            } else {
                log.warn("⚠️ 图片质量审核任务创建失败");
            }

        } catch (Exception e) {
            log.error("❌ 创建图片质量审核任务失败: assetId={}", asset.getAssetId(), e);
        }
    }


}
