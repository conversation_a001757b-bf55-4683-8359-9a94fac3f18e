package com.ybda.service;

import com.ybda.model.dto.DetectionRequestDTO;
import com.ybda.model.entity.DetectionRecord;

import java.util.Map;

/**
 * 检测服务接口
 */
public interface DetectionService {
    
    /**
     * 处理检测请求
     * 接收POST请求数据，保存检测记录和详情，确认待确认记录
     * 
     * @param detectionRequest 检测请求数据
     * @return 处理结果
     */
    Map<String, Object> processDetectionRequest(DetectionRequestDTO detectionRequest);
    
    /**
     * 确认待确认的资产检查记录
     * 根据检测记录的位置和时间，查找并确认相关的待确认记录
     * 
     * @param detectionRecord 检测记录
     */
    void confirmPendingAssetChecks(DetectionRecord detectionRecord);
    
    /**
     * 处理新增资产
     * 检测到新的资产时，将其添加到资产库中
     * 
     * @param detectionRecord 检测记录
     */
    void processNewAssets(DetectionRecord detectionRecord);

}
