package com.ybda.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据导出服务接口
 * 负责将巡检数据导出为文件格式
 */
public interface DataExportService {
    
    /**
     * 导出检测记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出的检测记录数据
     */
    Map<String, Object> exportDetectionRecords(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 导出巡检记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出的巡检记录数据
     */
    Map<String, Object> exportInspectionRecords(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 导出新增资产
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出的新增资产数据
     */
    Map<String, Object> exportNewAssets(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 导出报警记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出的报警记录数据
     */
    Map<String, Object> exportAlertRecords(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 打包图片文件
     * 
     * @param imagePaths 图片路径列表
     * @param outputPath 输出ZIP文件路径
     * @return 打包结果
     */
    Map<String, Object> packImages(java.util.List<String> imagePaths, String outputPath);
    
    /**
     * 生成完整的导出包
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param includeImages 是否包含图片
     * @param outputDir 输出目录
     * @return 导出包信息
     */
    Map<String, Object> generateExportPackage(LocalDateTime startTime, LocalDateTime endTime, 
                                             boolean includeImages, String outputDir);
}
