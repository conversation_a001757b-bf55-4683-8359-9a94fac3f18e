package com.ybda.service;

import com.ybda.model.entity.DetectionRecord;


/**
 * 资产完整性验证服务接口
 * 负责完整的资产验证体系：存在性验证 + 对应性验证
 */
public interface AssetIntegrityService {
    
    /**
     * 创建存在性检查任务
     * 当GPS经过资产位置时，创建期望检测任务
     *
     * @param deviceId        设备ID
     * @param expectedAssetId 期望的资产ID
     * @param assetType       资产类型
     * @param assetName       资产名称
     * @param latitude        GPS纬度
     * @param longitude       GPS经度
     * @param deviceLocationId  GPS建
     */
    void createExistenceCheck(String deviceId, String expectedAssetId, String assetType,
                              String assetName, Double latitude, Double longitude, String deviceLocationId);

    
    /**
     * 处理检测结果，确认待检查任务
     * 
     * @param detectionRecord 检测记录
     */
    void processDetectionResult(DetectionRecord detectionRecord);
    
    /**
     * 检查超时的验证任务
     * 定时任务调用，处理超时未确认的检查任务
     */
    void checkTimeoutTasks();

}
