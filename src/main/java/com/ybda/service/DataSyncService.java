package com.ybda.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据同步服务接口
 * 负责巡检数据与服务器之间的双向同步
 */
public interface DataSyncService {
    
    /**
     * 导出巡检数据
     * 将指定时间范围内的巡检数据导出为JSON文件
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param includeImages 是否包含图片文件
     * @return 导出结果，包含文件路径等信息
     */
    Map<String, Object> exportInspectionData(LocalDateTime startTime, LocalDateTime endTime, boolean includeImages);
    
    /**
     * 导入基础数据
     * 从JSON文件导入资产信息、配置等基础数据
     * 
     * @param dataFilePath 数据文件路径
     * @param imageFilePath 图片文件路径（可选）
     * @return 导入结果
     */
    Map<String, Object> importBaseData(String dataFilePath, String imageFilePath);
    
    /**
     * 获取待同步的数据统计
     * 
     * @param lastSyncTime 上次同步时间
     * @return 待同步数据统计信息
     */
    Map<String, Object> getPendingSyncStats(LocalDateTime lastSyncTime);
    
    /**
     * 检查网络连接状态
     * 
     * @return 网络状态信息
     */
    Map<String, Object> checkNetworkStatus();
    
    /**
     * 执行自动同步
     * 检测网络状态，如果可用则自动执行同步
     * 
     * @return 同步结果
     */
    Map<String, Object> performAutoSync();
    
    /**
     * 获取同步历史记录
     * 
     * @param limit 记录数量限制
     * @return 同步历史
     */
    Map<String, Object> getSyncHistory(int limit);
}
