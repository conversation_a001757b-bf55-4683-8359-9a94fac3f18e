package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.SyncRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 同步记录Mapper接口
 */
@Mapper
public interface SyncRecordMapper extends BaseMapper<SyncRecord> {
    
    /**
     * 查询最近的同步记录
     * 
     * @param syncType 同步类型
     * @param syncDirection 同步方向
     * @param limit 记录数量限制
     * @return 同步记录列表
     */
    @Select("SELECT * FROM sync_records WHERE sync_type = #{syncType} AND sync_direction = #{syncDirection} " +
            "ORDER BY created_time DESC LIMIT #{limit}")
    List<SyncRecord> findRecentSyncRecords(@Param("syncType") String syncType, 
                                          @Param("syncDirection") String syncDirection, 
                                          @Param("limit") int limit);
    
    /**
     * 查询最后一次成功的同步时间
     * 
     * @param syncType 同步类型
     * @param syncDirection 同步方向
     * @return 最后同步时间
     */
    @Select("SELECT MAX(end_time) FROM sync_records WHERE sync_type = #{syncType} " +
            "AND sync_direction = #{syncDirection} AND sync_status = 'SUCCESS'")
    LocalDateTime findLastSuccessfulSyncTime(@Param("syncType") String syncType, 
                                           @Param("syncDirection") String syncDirection);
    
    /**
     * 统计同步记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Select("SELECT sync_status, COUNT(*) as count FROM sync_records " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY sync_status")
    List<java.util.Map<String, Object>> countSyncRecords(@Param("startTime") LocalDateTime startTime, 
                                                         @Param("endTime") LocalDateTime endTime);
}
