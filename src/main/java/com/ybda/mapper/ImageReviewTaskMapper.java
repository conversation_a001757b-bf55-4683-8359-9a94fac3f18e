package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.ImageReviewTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片审核任务Mapper接口
 */
@Mapper
public interface ImageReviewTaskMapper extends BaseMapper<ImageReviewTask> {

    /**
     * 查询待审核的任务列表
     */
    List<ImageReviewTask> selectPendingTasks();

    /**
     * 根据资产ID查询审核任务
     */
    List<ImageReviewTask> selectByAssetId(@Param("assetId") String assetId);

    /**
     * 更新审核状态
     */
    int updateReviewStatus(@Param("id") Long id,
                          @Param("status") String status,
                          @Param("reviewerId") String reviewerId,
                          @Param("reason") String reason);
}
