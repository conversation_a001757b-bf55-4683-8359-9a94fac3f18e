package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.TrafficAsset;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交通资产Mapper接口
 */
@Mapper
public interface TrafficAssetMapper extends BaseMapper<TrafficAsset> {

    /**
     * 根据资产ID查询交通资产
     */
    TrafficAsset selectByAssetId(@Param("assetId") String assetId);

    /**
     * 查找指定位置附近的资产
     * @param latitude 纬度
     * @param longitude 经度
     * @param distance 距离(米)
     * @return 附近的资产列表
     */
    List<TrafficAsset> findNearbyAssets(@Param("latitude") Double latitude,
                                       @Param("longitude") Double longitude,
                                       @Param("distance") Double distance);

    /**
     * 根据地理范围查询资产
     * @param minLat 最小纬度
     * @param maxLat 最大纬度
     * @param minLng 最小经度
     * @param maxLng 最大经度
     * @return 范围内的资产列表
     */
    List<TrafficAsset> selectByBounds(@Param("minLat") Double minLat,
                                     @Param("maxLat") Double maxLat,
                                     @Param("minLng") Double minLng,
                                     @Param("maxLng") Double maxLng);

    /**
     * 更新资产状态
     */
    int updateStatus(@Param("assetId") String assetId, @Param("status") String status);

    /**
     * 更新资产检测信息
     */
    int updateDetectionInfo(@Param("assetId") String assetId,
                           @Param("lastDetectedTime") java.time.LocalDateTime lastDetectedTime,
                           @Param("detectionCount") Integer detectionCount);

    /**
     * 更新资产检测信息（包含图片URL）
     */
    int updateDetectionInfoWithImage(@Param("assetId") String assetId,
                                    @Param("lastDetectedTime") java.time.LocalDateTime lastDetectedTime,
                                    @Param("detectionCount") Integer detectionCount,
                                    @Param("imageUrl") String imageUrl);

    /**
     * 更新图片质量审核状态
     */
    int updateImageQualityStatus(@Param("assetId") String assetId,
                                @Param("status") String status,
                                @Param("reviewerId") String reviewerId,
                                @Param("reviewTime") java.time.LocalDateTime reviewTime,
                                @Param("reason") String reason);

    /**
     * 统计各状态的资产数量
     */
    List<java.util.Map<String, Object>> countByStatus();

    /**
     * 统计各类型的资产数量
     */
    List<java.util.Map<String, Object>> countByType();

    /**
     * 查询缺失的资产
     */
    List<TrafficAsset> selectMissingAssets();

    /**
     * 查找指定位置附近的点状资产
     */
    List<TrafficAsset> findNearbyPointAssets(@Param("latitude") Double latitude,
                                            @Param("longitude") Double longitude,
                                            @Param("distance") Double distance);

    /**
     * 查找所有线状和面状资产（用于复杂几何判断）
     */
    List<TrafficAsset> findComplexGeometryAssets();

    /**
     * 更新资产的几何坐标数据
     */
    int updateGeometryCoordinates(@Param("assetId") String assetId,
                                 @Param("geometryCoordinates") String geometryCoordinates);

    /**
     * 软删除资产（设置为不可用）
     */
    int softDeleteAsset(@Param("assetId") String assetId);

    /**
     * 恢复资产（设置为可用）
     */
    int restoreAsset(@Param("assetId") String assetId);

    /**
     * 查找指定类型和名称的线状资产（用于智能合并）
     */
    List<TrafficAsset> findLineAssetsByTypeAndName(@Param("type") String type, @Param("name") String name);
}
