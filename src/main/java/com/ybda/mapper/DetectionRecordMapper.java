package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.DetectionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 检测记录Mapper接口
 */
@Mapper
public interface DetectionRecordMapper extends BaseMapper<DetectionRecord> {
    /**
     * 更新处理状态
     */
    int updateProcessStatus(@Param("id") Long id, @Param("processStatus") String processStatus);
}
