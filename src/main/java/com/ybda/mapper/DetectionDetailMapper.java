package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.DetectionDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检测详情Mapper接口
 */
@Mapper
public interface DetectionDetailMapper extends BaseMapper<DetectionDetail> {
    
    /**
     * 批量插入检测详情
     */
    int batchInsert(@Param("details") List<DetectionDetail> details);
    
    /**
     * 根据检测记录ID查询详情列表
     */
    List<DetectionDetail> selectByDetectionRecordId(@Param("detectionRecordId") Long detectionRecordId);

    /**
     * 更新关联的资产ID
     */
    int updateTrafficAssetId(@Param("id") Long id, @Param("trafficAssetId") Long trafficAssetId);

}
