package com.ybda.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ybda.model.entity.InspectionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查记录Mapper接口 - 通用资产巡检记录
 */
@Mapper
public interface InspectionRecordMapper extends BaseMapper<InspectionRecord> {
    /**
     * 查询资产的最新N次巡查记录
     */
    List<InspectionRecord> selectLatestByAssetId(@Param("assetId") String assetId, @Param("limit") Integer limit);

    /**
     * 获取资产的下一个巡查次数
     */
    Integer getNextInspectionNumberByAsset(@Param("assetId") String assetId);


    /**
     * 删除资产的旧巡查记录（保留最新N条）
     */
    int deleteOldAssetRecords(@Param("assetId") String assetId, @Param("keepCount") Integer keepCount);

    /**
     * 根据巡检类型查询记录
     */
    List<InspectionRecord> selectByInspectionType(@Param("inspectionType") String inspectionType);

    /**
     * 查找指定位置附近的进行中标牌巡检
     */
    List<InspectionRecord> findNearbyActiveSignInspections(@Param("latitude") Double latitude,
                                                          @Param("longitude") Double longitude,
                                                          @Param("distance") Double distance,
                                                          @Param("signName") String signName);

    /**
     * 查询超时的巡检记录
     */
    List<InspectionRecord> selectTimeoutInspections();

    /**
     * 根据标牌名称和位置查询最新的巡检次数
     */
    Integer getNextInspectionNumberBySignAndLocation(@Param("signName") String signName,
                                                    @Param("latitude") Double latitude,
                                                    @Param("longitude") Double longitude,
                                                    @Param("distance") Double distance);
}
